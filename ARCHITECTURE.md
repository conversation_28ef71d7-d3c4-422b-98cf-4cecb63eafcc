# 小说创作管理器架构设计

## 目录结构

```
Novel_Creation_Manager/
├── src/                          # 源代码目录
│   ├── main/                     # Electron主进程
│   │   ├── index.ts             # 主进程入口
│   │   ├── ipc/                 # IPC通信处理
│   │   ├── database/            # 数据库管理
│   │   ├── ai/                  # AI服务集成
│   │   └── utils/               # 主进程工具
│   ├── renderer/                # 渲染进程（前端）
│   │   ├── components/          # React组件
│   │   │   ├── common/         # 通用组件
│   │   │   ├── editor/         # 编辑器组件
│   │   │   ├── outline/        # 大纲组件
│   │   │   ├── materials/      # 素材管理组件
│   │   │   └── ai/             # AI功能组件
│   │   ├── pages/               # 页面组件
│   │   │   ├── editor/         # 编辑器页面
│   │   │   ├── outline/         # 大纲页面
│   │   │   ├── materials/      # 素材库页面
│   │   │   └── settings/        # 设置页面
│   │   ├── stores/              # 状态管理
│   │   │   ├── novel.ts        # 小说数据状态
│   │   │   ├── editor.ts       # 编辑器状态
│   │   │   ├── ai.ts           # AI服务状态
│   │   │   └── ui.ts           # UI状态
│   │   ├── services/           # 业务逻辑服务
│   │   │   ├── novel/          # 小说服务
│   │   │   ├── ai/             # AI服务
│   │   │   ├── export/         # 导出服务
│   │   │   └── storage/        # 存储服务
│   │   ├── utils/               # 工具函数
│   │   │   ├── text/           # 文本处理
│   │   │   ├── file/           # 文件操作
│   │   │   ├── date/           # 日期处理
│   │   │   └── validation/     # 数据验证
│   │   ├── types/              # TypeScript类型定义
│   │   ├── hooks/              # React Hooks
│   │   ├── styles/             # 样式文件
│   │   └── assets/             # 静态资源
│   ├── shared/                 # 共享代码
│   │   ├── types/              # 共享类型
│   │   ├── constants/          # 常量定义
│   │   └── utils/              # 共享工具
│   └── preload/                # 预加载脚本
├── electron/                   # Electron配置
├── database/                   # 数据库相关
│   ├── migrations/             # 数据库迁移
│   └── seeds/                  # 初始数据
├── ai-services/                # AI服务
│   ├── providers/              # AI提供商
│   │   ├── openai/            # OpenAI接口
│   │   ├── zhipu/             # 智谱清言
│   │   ├── qwen/              # 通义千问
│   │   └── doubao/            # 豆包
│   └── utils/                 # AI工具函数
├── docs/                       # 文档
├── tests/                      # 测试文件
└── scripts/                    # 构建脚本
```

## 核心模块设计

### 1. 数据模型
- **Novel**: 小说基本信息
- **Chapter**: 章节内容
- **Character**: 角色信息
- **Outline**: 大纲结构
- **Material**: 素材库
- **Setting**: 应用设置

### 2. 功能模块
- **编辑器**: 富文本编辑，实时保存
- **大纲管理**: 层级结构，拖拽排序
- **AI助手**: 智能续写，文本润色
- **素材库**: 分类管理，快速检索
- **导出功能**: 多格式支持

### 3. 技术特点
- **模块化**: 每个模块独立，易于维护
- **类型安全**: 完整的TypeScript类型
- **响应式**: 现代化UI设计
- **本地优先**: 数据本地存储
- **AI集成**: 多模型支持