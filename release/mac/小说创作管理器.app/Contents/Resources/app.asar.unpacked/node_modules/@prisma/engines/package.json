{"name": "@prisma/engines", "version": "5.22.0", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/engines"}, "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "devDependencies": {"@swc/core": "1.6.13", "@swc/jest": "0.2.36", "@types/jest": "29.5.12", "@types/node": "18.19.31", "execa": "5.1.1", "jest": "29.7.0", "typescript": "5.4.5"}, "dependencies": {"@prisma/engines-version": "5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2", "@prisma/debug": "5.22.0", "@prisma/fetch-engine": "5.22.0", "@prisma/get-platform": "5.22.0"}, "files": ["dist", "download", "scripts"], "sideEffects": false}