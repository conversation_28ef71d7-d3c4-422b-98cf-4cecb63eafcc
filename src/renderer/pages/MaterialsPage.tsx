import React, { useState } from 'react'
import styled from 'styled-components'

const MaterialsContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const MaterialsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const MaterialsTitle = styled.h2`
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const MaterialsContent = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`

const Sidebar = styled.div`
  width: 250px;
  background: ${props => props.theme.colors.surface};
  border-right: 1px solid ${props => props.theme.colors.border};
  overflow-y: auto;
`

const CategoryItem = styled.div<{ active?: boolean }>`
  padding: ${props => props.theme.spacing.md};
  cursor: pointer;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.active ? props.theme.colors.primary + '10' : 'transparent'};
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text};
  
  &:hover {
    background: ${props => props.theme.colors.border + '20'};
  }
`

const MainContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
`

const SearchBar = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`

const SearchInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 4px;
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`

const ActionButton = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.primary}10;
    border-color: ${props => props.theme.colors.primary};
  }
`

const MaterialsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.md};
`

const MaterialCard = styled.div`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  padding: ${props => props.theme.spacing.md};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
`

const MaterialCardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${props => props.theme.spacing.sm};
`

const MaterialTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`

const MaterialType = styled.span`
  font-size: 12px;
  padding: 2px 8px;
  background: ${props => props.theme.colors.primary}20;
  color: ${props => props.theme.colors.primary};
  border-radius: 12px;
`

const MaterialDescription = styled.p`
  margin: 0;
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`

const MaterialTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.xs};
  margin-top: ${props => props.theme.spacing.sm};
`

const MaterialTag = styled.span`
  font-size: 12px;
  padding: 2px 6px;
  background: ${props => props.theme.colors.border};
  color: ${props => props.theme.colors.textSecondary};
  border-radius: 4px;
`

const MaterialDate = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: ${props => props.theme.spacing.sm};
`

interface Material {
  id: string
  title: string
  description: string
  type: 'character' | 'setting' | 'plot' | 'dialogue' | 'research' | 'other'
  tags: string[]
  content: string
  createdAt: Date
  updatedAt: Date
}

const MaterialsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [materials] = useState<Material[]>([
    {
      id: '1',
      title: '主角设定',
      description: '故事主要人物的性格特点和背景',
      type: 'character',
      tags: ['主角', '性格', '背景'],
      content: '主角是一个勇敢而善良的年轻人...',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      title: '古代城市场景',
      description: '故事发生的主要场景描述',
      type: 'setting',
      tags: ['场景', '城市', '古代'],
      content: '繁华的古代城市，街道上人来人往...',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '3',
      title: '关键剧情转折',
      description: '故事的主要冲突和转折点',
      type: 'plot',
      tags: ['剧情', '转折', '冲突'],
      content: '主角在关键时刻面临重要选择...',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ])

  const categories = [
    { id: 'all', name: '全部素材', count: materials.length },
    { id: 'character', name: '角色设定', count: materials.filter(m => m.type === 'character').length },
    { id: 'setting', name: '场景设定', count: materials.filter(m => m.type === 'setting').length },
    { id: 'plot', name: '剧情素材', count: materials.filter(m => m.type === 'plot').length },
    { id: 'dialogue', name: '对话素材', count: materials.filter(m => m.type === 'dialogue').length },
    { id: 'research', name: '研究资料', count: materials.filter(m => m.type === 'research').length },
    { id: 'other', name: '其他', count: materials.filter(m => m.type === 'other').length }
  ]

  const filteredMaterials = materials.filter(material => {
    const matchesCategory = selectedCategory === 'all' || material.type === selectedCategory
    const matchesSearch = material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const getTypeLabel = (type: string) => {
    const labels = {
      character: '角色',
      setting: '场景',
      plot: '剧情',
      dialogue: '对话',
      research: '研究',
      other: '其他'
    }
    return labels[type as keyof typeof labels] || type
  }

  const handleCreateMaterial = () => {
    // 这里需要实现创建新素材的逻辑
  }

  return (
    <MaterialsContainer>
      <MaterialsHeader>
        <MaterialsTitle>素材库</MaterialsTitle>
        <ActionButton onClick={handleCreateMaterial}>
          + 新建素材
        </ActionButton>
      </MaterialsHeader>
      
      <MaterialsContent>
        <Sidebar>
          {categories.map(category => (
            <CategoryItem
              key={category.id}
              active={selectedCategory === category.id}
              onClick={() => setSelectedCategory(category.id)}
            >
              <div>{category.name}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {category.count} 项
              </div>
            </CategoryItem>
          ))}
        </Sidebar>
        
        <MainContent>
          <SearchBar>
            <SearchInput
              type="text"
              placeholder="搜索素材..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </SearchBar>
          
          <MaterialsGrid>
            {filteredMaterials.map(material => (
              <MaterialCard key={material.id}>
                <MaterialCardHeader>
                  <MaterialTitle>{material.title}</MaterialTitle>
                  <MaterialType>{getTypeLabel(material.type)}</MaterialType>
                </MaterialCardHeader>
                
                <MaterialDescription>
                  {material.description}
                </MaterialDescription>
                
                <MaterialTags>
                  {material.tags.map(tag => (
                    <MaterialTag key={tag}>{tag}</MaterialTag>
                  ))}
                </MaterialTags>
                
                <MaterialDate>
                  更新于 {material.updatedAt.toLocaleDateString()}
                </MaterialDate>
              </MaterialCard>
            ))}
          </MaterialsGrid>
          
          {filteredMaterials.length === 0 && (
            <div style={{ textAlign: 'center', color: '#666', marginTop: '50px' }}>
              没有找到匹配的素材
            </div>
          )}
        </MainContent>
      </MaterialsContent>
    </MaterialsContainer>
  )
}

export default MaterialsPage