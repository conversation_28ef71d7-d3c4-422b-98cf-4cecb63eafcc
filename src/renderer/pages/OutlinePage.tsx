import React, { useState } from 'react'
import styled from 'styled-components'

const OutlineContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const OutlineHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const OutlineTitle = styled.h2`
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const OutlineContent = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`

const OutlineSidebar = styled.div`
  width: 300px;
  background: ${props => props.theme.colors.surface};
  border-right: 1px solid ${props => props.theme.colors.border};
  overflow-y: auto;
`

const OutlineMain = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`

const OutlineToolbar = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const ActionButton = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.primary + '10'};
    border-color: ${props => props.theme.colors.primary};
  }
`

const OutlineCanvas = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
`

const ChapterList = styled.div`
  padding: ${props => props.theme.spacing.sm};
`

const ChapterItem = styled.div<{ active?: boolean }>`
  padding: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.xs};
  border-radius: 4px;
  cursor: pointer;
  background: ${props => props.active ? props.theme.colors.primary + '10' : 'transparent'};
  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};
  
  &:hover {
    background: ${props => props.theme.colors.border + '20'};
  }
`

const ChapterTitle = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
`

const ChapterDescription = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`

const ChapterScene = styled.div`
  margin-left: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-left: 1px solid ${props => props.theme.colors.border};
  margin-bottom: ${props => props.theme.spacing.xs};
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`

const OutlineStructure = styled.div`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  padding: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.md};
`

const StructureTitle = styled.h3`
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`

const StructureItem = styled.div<{ level: number }>`
  margin-left: ${props => props.level * 20}px;
  padding: ${props => props.theme.spacing.sm};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:last-child {
    border-bottom: none;
  }
`

const StructureItemTitle = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 4px;
`

const StructureItemContent = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textSecondary};
`

const AddButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px dashed ${props => props.theme.colors.border};
  background: transparent;
  color: ${props => props.theme.colors.textSecondary};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.primary};
  }
`

interface Chapter {
  id: string
  title: string
  description: string
  scenes: Scene[]
  wordCount: number
  order: number
}

interface Scene {
  id: string
  title: string
  description: string
  location: string
  characters: string[]
  wordCount: number
  order: number
}

interface OutlineNode {
  id: string
  title: string
  content: string
  type: 'act' | 'chapter' | 'scene'
  level: number
  children: OutlineNode[]
}

const OutlinePage: React.FC = () => {
  const [selectedChapter, setSelectedChapter] = useState<string | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([
    {
      id: '1',
      title: '第一章：开端',
      description: '故事的开端，介绍主要角色和背景',
      scenes: [
        {
          id: '1-1',
          title: '场景一：相遇',
          description: '主角的第一次相遇',
          location: '咖啡厅',
          characters: ['主角A', '主角B'],
          wordCount: 1200,
          order: 1
        },
        {
          id: '1-2',
          title: '场景二：冲突',
          description: '两人之间的第一次冲突',
          location: '街道',
          characters: ['主角A', '主角B'],
          wordCount: 800,
          order: 2
        }
      ],
      wordCount: 2000,
      order: 1
    },
    {
      id: '2',
      title: '第二章：发展',
      description: '故事情节的发展，矛盾加深',
      scenes: [
        {
          id: '2-1',
          title: '场景一：调查',
          description: '主角开始调查事件真相',
          location: '图书馆',
          characters: ['主角A'],
          wordCount: 1500,
          order: 1
        }
      ],
      wordCount: 1500,
      order: 2
    }
  ])

  const outlineStructure: OutlineNode[] = [
    {
      id: 'act1',
      title: '第一幕：开端',
      content: '介绍故事背景和主要角色',
      type: 'act',
      level: 0,
      children: [
        {
          id: 'ch1',
          title: '第一章：相遇',
          content: '两位主角的第一次相遇',
          type: 'chapter',
          level: 1,
          children: [
            {
              id: 'sc1',
              title: '场景一：咖啡厅',
              content: '在咖啡厅的偶遇',
              type: 'scene',
              level: 2,
              children: []
            }
          ]
        }
      ]
    },
    {
      id: 'act2',
      title: '第二幕：发展',
      content: '矛盾冲突的发展',
      type: 'act',
      level: 0,
      children: []
    },
    {
      id: 'act3',
      title: '第三幕：高潮',
      content: '故事达到高潮',
      type: 'act',
      level: 0,
      children: []
    }
  ]

  const handleAddChapter = () => {
    const newChapter: Chapter = {
      id: Date.now().toString(),
      title: `第${chapters.length + 1}章`,
      description: '新章节描述',
      scenes: [],
      wordCount: 0,
      order: chapters.length + 1
    }
    setChapters([...chapters, newChapter])
  }

  const handleAddScene = (chapterId: string) => {
    setChapters(chapters.map(chapter => {
      if (chapter.id === chapterId) {
        const newScene: Scene = {
          id: `${chapterId}-${chapter.scenes.length + 1}`,
          title: `场景${chapter.scenes.length + 1}`,
          description: '新场景描述',
          location: '',
          characters: [],
          wordCount: 0,
          order: chapter.scenes.length + 1
        }
        return {
          ...chapter,
          scenes: [...chapter.scenes, newScene]
        }
      }
      return chapter
    }))
  }

  const totalWordCount = chapters.reduce((total, chapter) => total + chapter.wordCount, 0)

  const renderOutlineNode = (node: OutlineNode) => (
    <StructureItem key={node.id} level={node.level}>
      <StructureItemTitle>{node.title}</StructureItemTitle>
      <StructureItemContent>{node.content}</StructureItemContent>
      {node.children.map(child => renderOutlineNode(child))}
    </StructureItem>
  )

  return (
    <OutlineContainer>
      <OutlineHeader>
        <OutlineTitle>故事大纲</OutlineTitle>
        <div style={{ fontSize: '14px', color: '#666' }}>
          总字数: {totalWordCount}
        </div>
      </OutlineHeader>
      
      <OutlineContent>
        <OutlineSidebar>
          <div style={{ padding: '16px', borderBottom: '1px solid #eee' }}>
            <ActionButton onClick={handleAddChapter} style={{ width: '100%' }}>
              + 新建章节
            </ActionButton>
          </div>
          
          <ChapterList>
            {chapters.map(chapter => (
              <div key={chapter.id}>
                <ChapterItem
                  active={selectedChapter === chapter.id}
                  onClick={() => setSelectedChapter(chapter.id)}
                >
                  <ChapterTitle>{chapter.title}</ChapterTitle>
                  <ChapterDescription>
                    {chapter.description} • {chapter.wordCount}字
                  </ChapterDescription>
                </ChapterItem>
                
                {chapter.scenes.map(scene => (
                  <ChapterScene key={scene.id}>
                    {scene.title} • {scene.wordCount}字
                  </ChapterScene>
                ))}
                
                <AddButton onClick={() => handleAddScene(chapter.id)}>
                  + 添加场景
                </AddButton>
              </div>
            ))}
          </ChapterList>
        </OutlineSidebar>
        
        <OutlineMain>
          <OutlineToolbar>
            <ActionButton>导出大纲</ActionButton>
            <ActionButton>AI 优化</ActionButton>
            <ActionButton>时间线视图</ActionButton>
            <ActionButton>关系图谱</ActionButton>
          </OutlineToolbar>
          
          <OutlineCanvas>
            <OutlineStructure>
              <StructureTitle>故事结构</StructureTitle>
              {outlineStructure.map(node => renderOutlineNode(node))}
            </OutlineStructure>
            
            {selectedChapter && (
              <OutlineStructure>
                <StructureTitle>章节详情</StructureTitle>
                {(() => {
                  const chapter = chapters.find(c => c.id === selectedChapter)
                  if (!chapter) return null
                  
                  return (
                    <div>
                      <StructureItem level={0}>
                        <StructureItemTitle>{chapter.title}</StructureItemTitle>
                        <StructureItemContent>{chapter.description}</StructureItemContent>
                      </StructureItem>
                      
                      {chapter.scenes.map(scene => (
                        <StructureItem key={scene.id} level={1}>
                          <StructureItemTitle>{scene.title}</StructureItemTitle>
                          <StructureItemContent>
                            {scene.description}
                            <br />
                            <small>地点: {scene.location} | 角色: {scene.characters.join(', ')}</small>
                          </StructureItemContent>
                        </StructureItem>
                      ))}
                    </div>
                  )
                })()}
              </OutlineStructure>
            )}
          </OutlineCanvas>
        </OutlineMain>
      </OutlineContent>
    </OutlineContainer>
  )
}

export default OutlinePage