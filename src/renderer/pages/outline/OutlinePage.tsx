import React from 'react'
import styled from 'styled-components'

const OutlineContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const OutlineHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
`

const OutlineContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
`

const OutlinePage: React.FC = () => {
  return (
    <OutlineContainer>
      <OutlineHeader>
        <h1>故事大纲</h1>
      </OutlineHeader>
      <OutlineContent>
        <p>大纲管理功能开发中...</p>
      </OutlineContent>
    </OutlineContainer>
  )
}

export default OutlinePage