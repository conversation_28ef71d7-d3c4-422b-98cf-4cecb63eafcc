import React, { useEffect, useState, useCallback } from 'react'
import styled from 'styled-components'
import { useNovelStore } from '../../stores/novel'
import { Chapter } from '../../../shared/types'

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const EditorHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const EditorTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`

const EditorActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`

const Sidebar = styled.div`
  width: 250px;
  border-right: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  overflow-y: auto;
`

const MainEditor = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`

const Toolbar = styled.div`
  padding: ${props => props.theme.spacing.sm};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`

const EditorArea = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
`

const Button = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  
  &:hover {
    background: ${props => props.theme.colors.primaryHover};
  }
  
  &:disabled {
    background: ${props => props.theme.colors.secondary};
    cursor: not-allowed;
  }
`

const ChapterList = styled.div`
  padding: ${props => props.theme.spacing.sm};
`

const ChapterItem = styled.div`
  padding: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  
  &:hover {
    background: ${props => props.theme.colors.border};
  }
  
  &.active {
    background: ${props => props.theme.colors.primary}20;
    color: ${props => props.theme.colors.primary};
  }
`

const EditorPage: React.FC = () => {
  const { currentNovel, chapters } = useNovelStore()
  const [currentChapter, setCurrentChapter] = useState<Chapter | null>(null)
  const [content, setContent] = useState('')

  useEffect(() => {
    if (currentNovel) {
      // 加载章节
    }
  }, [currentNovel])

  const handleSave = useCallback(async () => {
    if (!currentChapter) return
    
    try {
      // 保存章节内容
    } catch (error) {
    }
  }, [currentChapter])

  const handleNewChapter = useCallback(async () => {
    if (!currentNovel) return
    
    try {
      // 创建新章节
    } catch (error) {
    }
  }, [currentNovel])

  return (
    <EditorContainer>
      <EditorHeader>
        <EditorTitle>
          {currentNovel ? currentNovel.title : '小说创作管理器'}
        </EditorTitle>
        <EditorActions>
          <Button onClick={handleSave} disabled={!currentChapter}>
            保存
          </Button>
          <Button onClick={handleNewChapter} disabled={!currentNovel}>
            新建章节
          </Button>
        </EditorActions>
      </EditorHeader>
      
      <EditorContent>
        <Sidebar>
          <ChapterList>
            {chapters.map(chapter => (
              <ChapterItem
                key={chapter.id}
                className={currentChapter?.id === chapter.id ? 'active' : ''}
                onClick={() => setCurrentChapter(chapter)}
              >
                {chapter.title}
              </ChapterItem>
            ))}
          </ChapterList>
        </Sidebar>
        
        <MainEditor>
          <Toolbar>
            <Button>粗体</Button>
            <Button>斜体</Button>
            <Button>下划线</Button>
          </Toolbar>
          
          <EditorArea>
            {currentChapter ? (
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="开始写作..."
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  outline: 'none',
                  resize: 'none',
                  background: 'transparent',
                  fontFamily: 'inherit',
                  fontSize: '16px',
                  lineHeight: '1.6'
                }}
              />
            ) : (
              <div style={{ textAlign: 'center', color: '#999' }}>
                {currentNovel ? '请选择或创建章节' : '请先创建或打开小说'}
              </div>
            )}
          </EditorArea>
        </MainEditor>
      </EditorContent>
    </EditorContainer>
  )
}

export default EditorPage