import React from 'react'
import styled from 'styled-components'

const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const SettingsHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
`

const SettingsContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
`

const SettingsPage: React.FC = () => {
  return (
    <SettingsContainer>
      <SettingsHeader>
        <h1>设置</h1>
      </SettingsHeader>
      <SettingsContent>
        <p>设置页面开发中...</p>
      </SettingsContent>
    </SettingsContainer>
  )
}

export default SettingsPage