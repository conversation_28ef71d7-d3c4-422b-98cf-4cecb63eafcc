import React, { useState } from 'react'
import styled from 'styled-components'
import { useUIStore } from '../stores/ui'

const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const SettingsTitle = styled.h2`
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const SettingsContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;
`

const SettingsSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`

const SectionTitle = styled.h3`
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 4px;
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 4px;
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
`

const Checkbox = styled.input`
  margin-right: ${props => props.theme.spacing.sm};
`

const Range = styled.input`
  width: 100%;
  margin: ${props => props.theme.spacing.sm} 0;
`

const Button = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.primary}10;
    border-color: ${props => props.theme.colors.primary};
  }
`

const SettingsTabs = styled.div`
  display: flex;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  margin-bottom: ${props => props.theme.spacing.lg};
`

const Tab = styled.button<{ active?: boolean }>`
  padding: ${props => props.theme.spacing.md};
  border: none;
  background: transparent;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text};
  border-bottom: 2px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`

const HelpText = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: ${props => props.theme.spacing.xs};
`

const SettingsPage: React.FC = () => {
  const { 
    theme, 
    editor, 
    autoSave, 
    ai, 
    setTheme, 
    updateEditorSettings, 
    updateAutoSaveSettings, 
    updateAISettings 
  } = useUIStore()
  
  const [activeTab, setActiveTab] = useState('general')
  const [apiKey, setApiKey] = useState('')

  const tabs = [
    { id: 'general', name: '通用设置' },
    { id: 'editor', name: '编辑器' },
    { id: 'ai', name: 'AI 设置' },
    { id: 'export', name: '导出设置' }
  ]

  const handleSaveSettings = () => {
    // 这里需要实现保存设置的逻辑
  }

  return (
    <SettingsContainer>
      <SettingsHeader>
        <SettingsTitle>设置</SettingsTitle>
      </SettingsHeader>
      
      <SettingsContent>
        <SettingsTabs>
          {tabs.map(tab => (
            <Tab
              key={tab.id}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.name}
            </Tab>
          ))}
        </SettingsTabs>

        {activeTab === 'general' && (
          <SettingsSection>
            <SectionTitle>界面设置</SectionTitle>
            
            <FormGroup>
              <Label>主题</Label>
              <Select 
                value={theme} 
                onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
              >
                <option value="light">浅色主题</option>
                <option value="dark">深色主题</option>
                <option value="auto">跟随系统</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label>自动保存</Label>
              <div>
                <Checkbox
                  type="checkbox"
                  checked={autoSave.enabled}
                  onChange={(e) => updateAutoSaveSettings({ enabled: e.target.checked })}
                />
                启用自动保存
              </div>
              {autoSave.enabled && (
                <div>
                  <Label>保存间隔</Label>
                  <Select
                    value={autoSave.interval}
                    onChange={(e) => updateAutoSaveSettings({ interval: parseInt(e.target.value) })}
                  >
                    <option value={10000}>10秒</option>
                    <option value={30000}>30秒</option>
                    <option value={60000}>1分钟</option>
                    <option value={300000}>5分钟</option>
                  </Select>
                </div>
              )}
            </FormGroup>
          </SettingsSection>
        )}

        {activeTab === 'editor' && (
          <SettingsSection>
            <SectionTitle>编辑器设置</SectionTitle>
            
            <FormGroup>
              <Label>字体大小</Label>
              <Range
                type="range"
                min="12"
                max="24"
                value={editor.fontSize}
                onChange={(e) => updateEditorSettings({ fontSize: parseInt(e.target.value) })}
              />
              <div style={{ textAlign: 'center' }}>{editor.fontSize}px</div>
            </FormGroup>

            <FormGroup>
              <Label>字体</Label>
              <Select
                value={editor.fontFamily}
                onChange={(e) => updateEditorSettings({ fontFamily: e.target.value })}
              >
                <option value="system-ui, -apple-system, sans-serif">系统默认</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="'Times New Roman', serif">Times New Roman</option>
                <option value="'Courier New', monospace">Courier New</option>
                <option value="'Microsoft YaHei', sans-serif">微软雅黑</option>
                <option value="'SimSun', serif">宋体</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label>行高</Label>
              <Range
                type="range"
                min="1.2"
                max="2.0"
                step="0.1"
                value={editor.lineHeight}
                onChange={(e) => updateEditorSettings({ lineHeight: parseFloat(e.target.value) })}
              />
              <div style={{ textAlign: 'center' }}>{editor.lineHeight}</div>
            </FormGroup>

            <FormGroup>
              <div>
                <Checkbox
                  type="checkbox"
                  checked={editor.wordWrap}
                  onChange={(e) => updateEditorSettings({ wordWrap: e.target.checked })}
                />
                自动换行
              </div>
            </FormGroup>

            <FormGroup>
              <div>
                <Checkbox
                  type="checkbox"
                  checked={editor.showLineNumbers}
                  onChange={(e) => updateEditorSettings({ showLineNumbers: e.target.checked })}
                />
                显示行号
              </div>
            </FormGroup>
          </SettingsSection>
        )}

        {activeTab === 'ai' && (
          <SettingsSection>
            <SectionTitle>AI 服务设置</SectionTitle>
            
            <FormGroup>
              <Label>默认 AI 提供商</Label>
              <Select
                value={ai.defaultProvider}
                onChange={(e) => updateAISettings({ defaultProvider: e.target.value })}
              >
                <option value="openai">OpenAI</option>
                <option value="zhipu">智谱AI</option>
                <option value="qwen">通义千问</option>
                <option value="doubao">豆包</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <div>
                <Checkbox
                  type="checkbox"
                  checked={ai.autoSuggest}
                  onChange={(e) => updateAISettings({ autoSuggest: e.target.checked })}
                />
                启用自动建议
              </div>
              <HelpText>
                在写作时自动显示AI建议
              </HelpText>
            </FormGroup>

            <FormGroup>
              <Label>OpenAI API Key</Label>
              <Input
                type="password"
                placeholder="输入您的 OpenAI API Key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
              />
              <HelpText>
                请在 OpenAI 官网获取您的 API Key
              </HelpText>
            </FormGroup>
          </SettingsSection>
        )}

        {activeTab === 'export' && (
          <SettingsSection>
            <SectionTitle>导出设置</SectionTitle>
            
            <FormGroup>
              <Label>默认导出格式</Label>
              <Select>
                <option value="markdown">Markdown</option>
                <option value="docx">Word 文档</option>
                <option value="txt">纯文本</option>
                <option value="pdf">PDF</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <div>
                <Checkbox type="checkbox" defaultChecked />
                导出时包含元数据
              </div>
              <HelpText>
                包含标题、作者、创建时间等信息
              </HelpText>
            </FormGroup>

            <FormGroup>
              <div>
                <Checkbox type="checkbox" defaultChecked />
                导出时包含大纲
              </div>
              <HelpText>
                将故事大纲一并导出
              </HelpText>
            </FormGroup>
          </SettingsSection>
        )}

        <div style={{ marginTop: '32px' }}>
          <Button onClick={handleSaveSettings}>
            保存设置
          </Button>
        </div>
      </SettingsContent>
    </SettingsContainer>
  )
}

export default SettingsPage