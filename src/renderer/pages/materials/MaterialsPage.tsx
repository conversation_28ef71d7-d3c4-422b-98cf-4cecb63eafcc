import React from 'react'
import styled from 'styled-components'

const MaterialsContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const MaterialsHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
`

const MaterialsContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
`

const MaterialsPage: React.FC = () => {
  return (
    <MaterialsContainer>
      <MaterialsHeader>
        <h1>素材库</h1>
      </MaterialsHeader>
      <MaterialsContent>
        <p>素材库功能开发中...</p>
      </MaterialsContent>
    </MaterialsContainer>
  )
}

export default MaterialsPage