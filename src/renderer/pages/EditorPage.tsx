import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import Editor from '../components/editor/Editor'
import AIPanel from '../components/ai/AIPanel'
import { useUIStore } from '../stores/ui'

const EditorPageContainer = styled.div`
  display: flex;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const EditorMain = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`

const EditorHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const DocumentTitle = styled.input`
  border: none;
  background: transparent;
  font-size: 18px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  outline: none;
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`

const EditorActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`

const ActionButton = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.primary}10;
    border-color: ${props => props.theme.colors.primary};
  }
`

const EditorLayout = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`

const AIPanelWrapper = styled.div<{ open: boolean; width: number }>`
  width: ${props => props.open ? `${props.width}px` : '0'};
  transition: width 0.3s ease;
  overflow: hidden;
`

const ToggleAIPanelButton = styled(ActionButton)`
  position: fixed;
  right: ${props => props.open ? `${props.width + 10}px` : '10px'};
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
`

interface Document {
  id: string
  title: string
  content: string
  createdAt: Date
  updatedAt: Date
}

const EditorPage: React.FC = () => {
  const { sidebar } = useUIStore()
  const [showAIPanel, setShowAIPanel] = useState(false)
  const [document, setDocument] = useState<Document>({
    id: '1',
    title: '未命名文档',
    content: '',
    createdAt: new Date(),
    updatedAt: new Date()
  })
  const [isSaving, setIsSaving] = useState(false)

  const handleContentChange = (content: string) => {
    setDocument(prev => ({
      ...prev,
      content,
      updatedAt: new Date()
    }))
  }

  const handleTitleChange = (title: string) => {
    setDocument(prev => ({
      ...prev,
      title,
      updatedAt: new Date()
    }))
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // 这里需要实现实际的保存逻辑
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch {
    } finally {
      setIsSaving(false)
    }
  }

  const handleAIResponse = (response: string, type: string) => {
    // 根据AI响应类型处理内容
    switch (type) {
      case 'continue':
        setDocument(prev => ({
          ...prev,
          content: prev.content + '\n\n' + response,
          updatedAt: new Date()
        }))
        break
      case 'polish':
        setDocument(prev => ({
          ...prev,
          content: response,
          updatedAt: new Date()
        }))
        break
      case 'correct':
        setDocument(prev => ({
          ...prev,
          content: response,
          updatedAt: new Date()
        }))
        break
      default:
    }
  }

  const handleExport = (_format: string) => {
    // 这里需要实现导出功能
  }

  useEffect(() => {
    // 模拟加载文档
    const loadDocument = async () => {
      await new Promise(resolve => setTimeout(resolve, 500))
      setDocument({
        id: '1',
        title: '示例文档',
        content: '这是一个示例文档。\n\n您可以在这里开始您的创作...', 
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    loadDocument()
  }, [])

  return (
    <EditorPageContainer>
      <EditorMain>
        <EditorHeader>
          <DocumentTitle
            value={document.title}
            onChange={(e) => handleTitleChange(e.target.value)}
            placeholder="输入文档标题..."
          />
          <EditorActions>
            <ActionButton onClick={() => handleExport('markdown')}> 
              导出 Markdown
            </ActionButton>
            <ActionButton onClick={() => handleExport('docx')}> 
              导出 Word
            </ActionButton>
            <ActionButton onClick={handleSave} disabled={isSaving}>
              {isSaving ? '保存中...' : '保存'}
            </ActionButton>
          </EditorActions>
        </EditorHeader>
        
        <EditorLayout>
          <EditorContent>
            <Editor
              content={document.content}
              onChange={handleContentChange}
              onSave={handleSave}
              placeholder="开始写作..."
            />
          </EditorContent>
          
          <AIPanelWrapper open={showAIPanel} width={sidebar.width}>
            <AIPanel
              content={document.content}
              onAIResponse={handleAIResponse}
            />
          </AIPanelWrapper>
        </EditorLayout>
      </EditorMain>
      
      <ToggleAIPanelButton
        open={showAIPanel}
        width={sidebar.width}
        onClick={() => setShowAIPanel(!showAIPanel)}
      >
        {showAIPanel ? '◀' : '▶'}
      </ToggleAIPanelButton>
    </EditorPageContainer>
  )
}

export default EditorPage