import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from 'styled-components'
import { GlobalStyle } from './styles/GlobalStyle'
import { theme } from './styles/theme'

// 页面组件
import EditorPage from './pages/EditorPage'
import OutlinePage from './pages/OutlinePage'
import MaterialsPage from './pages/MaterialsPage'
import SettingsPage from './pages/SettingsPage'

// 布局组件
import Layout from './components/common/Layout'
import Navigation from './components/common/Navigation'

// 状态管理
import { useNovelStore } from './stores/novel'
import { useUIStore } from './stores/ui'

const App: React.FC = () => {
  const { loadNovels } = useNovelStore()
  const { theme: currentTheme, loadTheme } = useUIStore()

  useEffect(() => {
    // 初始化应用
    loadTheme()
    loadNovels()
    
    // 监听来自主进程的菜单事件
    const handleMenuEvent = (event: object, action: string, data?: string) => {
      switch (action) {
        case 'new-novel':
          handleNewNovel()
          break
        case 'open-novel':
          if (data) handleOpenNovel()
          break
        case 'save':
          handleSave()
          break
        case 'export':
          if (data) handleExport()
          break
        case 'ai-assistant':
          handleAIAssistant()
          break
        case 'word-count':
          handleWordCount()
          break
        case 'distraction-free':
          handleDistractionFreeMode()
          break
      }
    }

    // @ts-ignore
    window.electron?.onMenuAction(handleMenuEvent)

    return () => {
      // @ts-ignore
      window.electron?.removeAllListeners('menu-action')
    }
  }, [loadNovels, loadTheme])

  const handleNewNovel = async () => {
    // 实现新建小说逻辑
  }

  const handleOpenNovel = async () => {
    // 实现打开小说逻辑
  }

  const handleSave = async () => {
    // 实现保存逻辑
  }

  const handleExport = async () => {
    // 实现导出逻辑
  }

  const handleAIAssistant = () => {
    // 实现AI助手逻辑
  }

  const handleWordCount = () => {
    // 实现字数统计逻辑
  }

  const handleDistractionFreeMode = () => {
    // 实现小黑屋模式
  }

  return (
    <ThemeProvider theme={theme[currentTheme]}>
      <GlobalStyle />
      <Router>
        <div className="app">
          <Layout>
            <Navigation />
            <Routes>
              <Route path="/" element={<EditorPage />} />
              <Route path="/editor" element={<EditorPage />} />
              <Route path="/outline" element={<OutlinePage />} />
              <Route path="/materials" element={<MaterialsPage />} />
              <Route path="/settings" element={<SettingsPage />} />
            </Routes>
          </Layout>
        </div>
      </Router>
    </ThemeProvider>
  )
}

export default App