import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

interface UIState {
  // 主题设置
  theme: 'light' | 'dark' | 'auto'
  
  // 编辑器设置
  editor: {
    fontSize: number
    fontFamily: string
    lineHeight: number
    showLineNumbers: boolean
    wordWrap: boolean
    minimap: boolean
  }
  
  // 自动保存设置
  autoSave: {
    enabled: boolean
    interval: number
  }
  
  // AI设置
  ai: {
    defaultProvider: string
    autoSuggest: boolean
  }
  
  // UI状态
  sidebar: {
    open: boolean
    width: number
  }
  
  // 操作方法
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  loadTheme: () => void
  updateEditorSettings: (settings: Partial<UIState['editor']>) => void
  updateAutoSaveSettings: (settings: Partial<UIState['autoSave']>) => void
  updateAISettings: (settings: Partial<UIState['ai']>) => void
  toggleSidebar: () => void
  setSidebarWidth: (width: number) => void
}

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        theme: 'light',
        editor: {
          fontSize: 16,
          fontFamily: 'system-ui, -apple-system, sans-serif',
          lineHeight: 1.6,
          showLineNumbers: false,
          wordWrap: true,
          minimap: false
        },
        autoSave: {
          enabled: true,
          interval: 30000 // 30秒
        },
        ai: {
          defaultProvider: 'openai',
          autoSuggest: true
        },
        sidebar: {
          open: true,
          width: 250
        },

        // 操作方法
        setTheme: (theme) => {
          set({ theme })
          // 应用主题到document
          if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark')
          } else if (theme === 'light') {
            document.documentElement.setAttribute('data-theme', 'light')
          } else {
            // 自动模式根据系统偏好
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
            document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light')
          }
        },

        loadTheme: () => {
          const { theme } = get()
          get().setTheme(theme)
        },

        updateEditorSettings: (settings) => {
          set(state => ({
            editor: { ...state.editor, ...settings }
          }))
        },

        updateAutoSaveSettings: (settings) => {
          set(state => ({
            autoSave: { ...state.autoSave, ...settings }
          }))
        },

        updateAISettings: (settings) => {
          set(state => ({
            ai: { ...state.ai, ...settings }
          }))
        },

        toggleSidebar: () => {
          set(state => ({
            sidebar: { ...state.sidebar, open: !state.sidebar.open }
          }))
        },

        setSidebarWidth: (width) => {
          set(state => ({
            sidebar: { ...state.sidebar, width }
          }))
        }
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          theme: state.theme,
          editor: state.editor,
          autoSave: state.autoSave,
          ai: state.ai
        })
      }
    ),
    { name: 'ui-store' }
  )
)