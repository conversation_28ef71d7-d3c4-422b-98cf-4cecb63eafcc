import React, { useState, useEffect, useCallback } from 'react'
import styled from 'styled-components'
import { useUIStore } from '../../stores/ui'

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${props => props.theme.colors.background};
`

const EditorToolbar = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  flex-wrap: wrap;
`

const ToolbarButton = styled.button<{ active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.active ? props.theme.colors.primary : props.theme.colors.surface};
  color: ${props => props.active ? 'white' : props.theme.colors.text};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.active ? props.theme.colors.primaryDark : props.theme.colors.border};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const ToolbarSeparator = styled.div`
  width: 1px;
  height: 24px;
  background: ${props => props.theme.colors.border};
`

const EditorContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
  
  &:focus {
    outline: none;
  }
`

const EditorTextArea = styled.textarea`
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.typography.fontSize};
  line-height: ${props => props.theme.typography.lineHeight};
  font-family: ${props => props.theme.typography.fontFamily};
  resize: none;
  outline: none;
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`

const StatusBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-top: 1px solid ${props => props.theme.colors.border};
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`

const WordCount = styled.span`
  font-weight: 500;
`

const SaveStatus = styled.span<{ saving?: boolean }>`
  color: ${props => props.saving ? props.theme.colors.warning : props.theme.colors.success};
`

interface EditorProps {
  content: string
  onChange: (content: string) => void
  onSave?: () => void
  placeholder?: string
  readOnly?: boolean
}

const Editor: React.FC<EditorProps> = ({
  content,
  onChange,
  onSave,
  placeholder = '开始写作...',
  readOnly = false
}) => {
  const { editor: editorSettings, autoSave } = useUIStore()
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)

  // 计算字数和字符数
  useEffect(() => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0).length
    const chars = content.length
    setWordCount(words)
    setCharCount(chars)
  }, [content])

  const handleSave = useCallback(async () => {
    if (!onSave || isSaving) return

    setIsSaving(true)
    try {
      await onSave()
      setLastSaved(new Date())
    } catch {
    } finally {
      setIsSaving(false)
    }
  }, [onSave, isSaving])

  // 自动保存功能
  useEffect(() => {
    if (!autoSave.enabled || !onSave) return

    const saveTimer = setTimeout(() => {
      handleSave()
    }, autoSave.interval)

    return () => clearTimeout(saveTimer)
  }, [content, autoSave.enabled, autoSave.interval, handleSave, onSave])

  const formatText = useCallback((_format: string) => {
    // 这里可以实现文本格式化功能
    // 例如：加粗、斜体、标题等
  }, [])

  const insertText = useCallback((_text: string) => {
    // 在光标位置插入文本
  }, [])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // 快捷键支持
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault()
          handleSave()
          break
        case 'b':
          e.preventDefault()
          formatText('bold')
          break
        case 'i':
          e.preventDefault()
          formatText('italic')
          break
        case 'u':
          e.preventDefault()
          formatText('underline')
          break
      }
    }
  }, [handleSave, formatText])

  const formatSaveTime = useCallback((date: Date | null) => {
    if (!date) return '未保存'
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const seconds = Math.floor(diff / 1000)
    
    if (seconds < 60) return '刚刚保存'
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟前保存`
    if (seconds < 86400) return `${Math.floor(seconds / 3600)}小时前保存`
    return date.toLocaleString()
  }, [])

  return (
    <EditorContainer>
      <EditorToolbar>
        <ToolbarButton
          onClick={() => formatText('bold')}
          title="加粗 (Ctrl+B)"
        >
          <strong>B</strong>
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('italic')}
          title="斜体 (Ctrl+I)"
        >
          <em>I</em>
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('underline')}
          title="下划线 (Ctrl+U)"
        >
          <u>U</u>
        </ToolbarButton>
        
        <ToolbarSeparator />
        
        <ToolbarButton
          onClick={() => formatText('heading1')}
          title="标题1"
        >
          H1
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('heading2')}
          title="标题2"
        >
          H2
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('heading3')}
          title="标题3"
        >
          H3
        </ToolbarButton>
        
        <ToolbarSeparator />
        
        <ToolbarButton
          onClick={() => insertText('•')}
          title="项目符号"
        >
          •
        </ToolbarButton>
        <ToolbarButton
          onClick={() => insertText('1.')}
          title="编号列表"
        >
          1.
        </ToolbarButton>
        
        <ToolbarSeparator />
        
        <ToolbarButton
          onClick={handleSave}
          disabled={isSaving}
          title="保存 (Ctrl+S)"
        >
          {isSaving ? '💾' : '💾'}
        </ToolbarButton>
      </EditorToolbar>
      
      <EditorContent>
        <EditorTextArea
          value={content}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          readOnly={readOnly}
          style={{
            fontSize: `${editorSettings.fontSize}px`,
            fontFamily: editorSettings.fontFamily,
            lineHeight: editorSettings.lineHeight
          }}
        />
      </EditorContent>
      
      <StatusBar>
        <div>
          <WordCount>{wordCount} 字</WordCount>
          <span> • </span>
          <span>{charCount} 字符</span>
        </div>
        <SaveStatus saving={isSaving}>
          {formatSaveTime(lastSaved)}
        </SaveStatus>
      </StatusBar>
    </EditorContainer>
  )
}

export default Editor