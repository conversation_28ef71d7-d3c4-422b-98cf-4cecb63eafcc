import React, { useState } from 'react'
import styled from 'styled-components'
import { useUIStore } from '../../stores/ui'

const AIPanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${props => props.theme.colors.background};
  border-left: 1px solid ${props => props.theme.colors.border};
`

const AIPanelHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const AIPanelTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const AIPanelContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
`

const AIButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  
  &:hover {
    background: ${props => props.theme.colors.primary}10;
    border-color: ${props => props.theme.colors.primary};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const AIButtonTitle = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`

const AIButtonDescription = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`

const AIResponseArea = styled.div`
  margin-top: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 6px;
  min-height: 100px;
`

const AIResponseText = styled.div`
  white-space: pre-wrap;
  color: ${props => props.theme.colors.text};
  line-height: 1.5;
`

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-top: 2px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`

const ProviderSelect = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 4px;
`

interface AIPanelProps {
  content: string
  onAIResponse: (response: string, type: string) => void
}

const AIPanel: React.FC<AIPanelProps> = ({ content, onAIResponse }) => {
  const { ai: aiSettings } = useUIStore()
  const [selectedProvider, setSelectedProvider] = useState(aiSettings.defaultProvider)
  const [loading, setLoading] = useState(false)
  const [response, setResponse] = useState<string>('')
  const [responseType, setResponseType] = useState<string>('')

  const handleAIAction = async (type: string) => {
    if (!content.trim()) return

    setLoading(true)
    setResponse('')
    setResponseType(type)

    try {
      // 这里需要调用实际的AI服务
      // 暂时模拟响应
      let mockResponse = ''
      switch (type) {
        case 'continue':
          mockResponse = '这是AI续写的内容示例...'
          break
        case 'polish':
          mockResponse = '这是AI润色后的文本示例...'
          break
        case 'correct':
          mockResponse = '这是AI纠正后的文本示例...'
          break
        case 'plot':
          mockResponse = '1. 剧情发展方向一\n2. 剧情发展方向二\n3. 剧情发展方向三'
          break
        default:
          mockResponse = 'AI响应示例...'
      }

      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟
      setResponse(mockResponse)
      onAIResponse(mockResponse, type)
    } catch (error) {
      console.error('AI action failed:', error)
      setResponse('AI服务暂时不可用，请稍后再试。')
    } finally {
      setLoading(false)
    }
  }

  const aiActions = [
    {
      type: 'continue',
      title: '智能续写',
      description: '基于当前内容智能续写故事',
      icon: '✍️'
    },
    {
      type: 'polish',
      title: '文本润色',
      description: '改善文笔和表达方式',
      icon: '✨'
    },
    {
      type: 'correct',
      title: '错字纠正',
      description: '自动检测并纠正错别字',
      icon: '🔍'
    },
    {
      type: 'plot',
      title: '剧情推荐',
      description: '提供剧情发展建议',
      icon: '💡'
    }
  ]

  return (
    <AIPanelContainer>
      <AIPanelHeader>
        <AIPanelTitle>AI 助手</AIPanelTitle>
      </AIPanelHeader>
      
      <AIPanelContent>
        <ProviderSelect
          value={selectedProvider}
          onChange={(e) => setSelectedProvider(e.target.value)}
        >
          <option value="openai">OpenAI</option>
          <option value="zhipu">智谱AI</option>
          <option value="qwen">通义千问</option>
          <option value="doubao">豆包</option>
        </ProviderSelect>
        
        {aiActions.map((action) => (
          <AIButton
            key={action.type}
            onClick={() => handleAIAction(action.type)}
            disabled={loading || !content.trim()}
          >
            <AIButtonTitle>
              {action.icon} {action.title}
            </AIButtonTitle>
            <AIButtonDescription>
              {action.description}
            </AIButtonDescription>
          </AIButton>
        ))}
        
        {loading && (
          <AIResponseArea>
            <LoadingSpinner /> AI正在思考中...
          </AIResponseArea>
        )}
        
        {response && (
          <AIResponseArea>
            <AIResponseText>{response}</AIResponseText>
          </AIResponseArea>
        )}
      </AIPanelContent>
    </AIPanelContainer>
  )
}

export default AIPanel