export interface Novel {
  id: string
  title: string
  author: string
  description: string
  coverImage?: string
  createdAt: Date
  updatedAt: Date
  wordCount: number
  chapterCount: number
  status: 'draft' | 'publishing' | 'published'
  genre: string[]
  tags: string[]
}

export interface Chapter {
  id: string
  novelId: string
  title: string
  content: string
  wordCount: number
  orderIndex: number
  status: 'draft' | 'completed'
  createdAt: Date
  updatedAt: Date
}

export interface Character {
  id: string
  novelId: string
  name: string
  alias?: string[]
  description: string
  age?: number
  gender: 'male' | 'female' | 'other'
  personality: string[]
  background: string
  createdAt: Date
  updatedAt: Date
}

export interface CharacterRelationship {
  id: string
  characterId: string
  targetCharacterId: string
  relationship: string
  description: string
}

export interface Outline {
  id: string
  novelId: string
  title: string
  content: string
  orderIndex: number
  level: number
  parentId?: string
  children?: Outline[]
  createdAt: Date
  updatedAt: Date
}

export interface Material {
  id: string
  novelId?: string
  title: string
  content: string
  type: 'text' | 'image' | 'reference' | 'note'
  category: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface AIServiceConfig {
  id: string
  name: string
  provider: 'openai' | 'zhipu' | 'qwen' | 'doubao'
  model: string
  apiKey?: string
  baseUrl?: string
  enabled: boolean
  config: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface AIRequest {
  prompt: string
  context?: string
  model?: string
  temperature?: number
  maxTokens?: number
}

export interface AIResponse {
  content: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model: string
}

export interface ExportOptions {
  format: 'doc' | 'markdown' | 'txt' | 'pdf'
  includeTOC: boolean
  includeChapterNumbers: boolean
  font?: string
  fontSize?: number
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  autoSave: {
    enabled: boolean
    interval: number
  }
  editor: {
    fontSize: number
    fontFamily: string
    lineHeight: number
    wordWrap: boolean
    showLineNumbers: boolean
  }
  ai: {
    defaultProvider: string
    autoSuggest: boolean
  }
  export: {
    defaultFormat: string
    includeMetadata: boolean
    includeOutline: boolean
  }
}

export interface BackupHistory {
  id: string
  novelId: string
  backupPath: string
  backupSize: number
  createdAt: Date
}