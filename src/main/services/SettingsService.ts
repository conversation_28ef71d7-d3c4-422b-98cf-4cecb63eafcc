import { DatabaseService } from '../database/DatabaseService'
import { AppSettings, AIServiceConfig, BackupHistory } from '../../shared/types'

export class SettingsService {
  private db: DatabaseService
  private defaultSettings: AppSettings = {
    theme: 'light',
    autoSave: {
      enabled: true,
      interval: 30000
    },
    editor: {
      fontSize: 16,
      fontFamily: 'system-ui, -apple-system, sans-serif',
      lineHeight: 1.6,
      wordWrap: true,
      showLineNumbers: false
    },
    ai: {
      defaultProvider: 'openai',
      autoSuggest: true
    },
    export: {
      defaultFormat: 'markdown',
      includeMetadata: true,
      includeOutline: true
    }
  }

  constructor(db: DatabaseService) {
    this.db = db
  }

  async getSettings(): Promise<AppSettings> {
    try {
      const settings: Partial<AppSettings> = {}
      
      // 获取各个设置项
      const theme = await this.getSetting('theme')
      if (theme) settings.theme = theme as 'light' | 'dark' | 'auto'

      const autoSave = await this.getSetting('autoSave')
      if (autoSave) settings.autoSave = JSON.parse(autoSave)

      const editor = await this.getSetting('editor')
      if (editor) settings.editor = JSON.parse(editor)

      const ai = await this.getSetting('ai')
      if (ai) settings.ai = JSON.parse(ai)

      const exportSettings = await this.getSetting('export')
      if (exportSettings) settings.export = JSON.parse(exportSettings)

      // 合并默认设置
      return this.mergeSettings(this.defaultSettings, settings)
    } catch {
      return this.defaultSettings
    }
  }

  async updateSettings(updates: Partial<AppSettings>): Promise<void> {
    try {
      for (const [key, value] of Object.entries(updates)) {
        await this.setSetting(key, JSON.stringify(value))
      }
    } catch (error) {
      throw error
    }
  }

  async getSetting(key: string): Promise<string | null> {
    const row = await this.db.get('SELECT value FROM app_settings WHERE key = ?', [key])
    return row ? row.value as string : null
  }

  async setSetting(key: string, value: string): Promise<void> {
    const existing = await this.getSetting(key)
    
    if (existing) {
      await this.db.run(
        'UPDATE app_settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?',
        [value, key]
      )
    } else {
      await this.db.run(
        'INSERT INTO app_settings (key, value) VALUES (?, ?)',
        [key, value]
      )
    }
  }

  async deleteSetting(key: string): Promise<void> {
    await this.db.run('DELETE FROM app_settings WHERE key = ?', [key])
  }

  async resetSettings(): Promise<void> {
    await this.db.run('DELETE FROM app_settings')
  }

  // AI 服务配置
  async getAIServiceConfig(provider: string): Promise<AIServiceConfig | null> {
    const row = await this.db.get('SELECT * FROM ai_services WHERE name = ?', [provider])
    if (!row) return null

    return {
      id: row.id as string,
      name: row.name as string,
      provider: row.provider as 'openai' | 'zhipu' | 'qwen' | 'doubao',
      model: row.model as string,
      apiKey: row.api_key as string,
      baseUrl: row.base_url as string,
      enabled: row.enabled === 1,
      config: JSON.parse(row.config as string || '{}'),
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string)
    }
  }

  async saveAIServiceConfig(config: Omit<AIServiceConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    const existing = await this.db.get('SELECT id FROM ai_services WHERE name = ?', [config.name])
    
    const data = {
      name: config.name,
      provider: config.provider,
      model: config.model,
      api_key: config.apiKey,
      base_url: config.baseUrl,
      enabled: config.enabled ? 1 : 0,
      config: JSON.stringify(config.config || {})
    }

    if (existing) {
      await this.db.run(
        'UPDATE ai_services SET provider = ?, model = ?, api_key = ?, base_url = ?, enabled = ?, config = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?',
        [data.provider, data.model, data.api_key, data.base_url, data.enabled, data.config, data.name]
      )
    } else {
      await this.db.run(
        'INSERT INTO ai_services (id, name, provider, model, api_key, base_url, enabled, config) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [Date.now().toString(), data.name, data.provider, data.model, data.api_key, data.base_url, data.enabled, data.config]
      )
    }
  }

  async getAIServiceConfigs(): Promise<AIServiceConfig[]> {
    const rows = await this.db.findAll('ai_services', '1=1 ORDER BY name')
    return rows.map(row => ({
      id: row.id as string,
      name: row.name as string,
      provider: row.provider as 'openai' | 'zhipu' | 'qwen' | 'doubao',
      model: row.model as string,
      apiKey: row.api_key as string,
      baseUrl: row.base_url as string,
      enabled: row.enabled === 1,
      config: JSON.parse(row.config as string || '{}'),
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string)
    }))
  }

  async deleteAIServiceConfig(name: string): Promise<void> {
    await this.db.run('DELETE FROM ai_services WHERE name = ?', [name])
  }

  // 备份相关
  async createBackup(novelId: string, backupPath: string, backupSize: number): Promise<void> {
    await this.db.run(
      'INSERT INTO backup_history (id, novel_id, backup_path, backup_size) VALUES (?, ?, ?, ?)',
      [Date.now().toString(), novelId, backupPath, backupSize]
    )
  }

  async getBackupHistory(novelId: string): Promise<BackupHistory[]> {
    const rows = await this.db.findAll('backup_history', 'novel_id = ? ORDER BY created_at DESC', [novelId])
    return rows.map(row => ({
      id: row.id as string,
      novelId: row.novel_id as string,
      backupPath: row.backup_path as string,
      backupSize: row.backup_size as number,
      createdAt: new Date(row.created_at as string)
    }))
  }

  async deleteBackup(backupId: string): Promise<void> {
    await this.db.run('DELETE FROM backup_history WHERE id = ?', [backupId])
  }

  private mergeSettings(defaultSettings: AppSettings, userSettings: Partial<AppSettings>): AppSettings {
    return {
      theme: userSettings.theme || defaultSettings.theme,
      autoSave: { ...defaultSettings.autoSave, ...userSettings.autoSave },
      editor: { ...defaultSettings.editor, ...userSettings.editor },
      ai: { ...defaultSettings.ai, ...userSettings.ai },
      export: { ...defaultSettings.export, ...userSettings.export }
    }
  }
}