import { app } from 'electron'
import path from 'path'
import fs from 'fs'
import { DatabaseService } from '../database/DatabaseService'
import { NovelService } from '../services/NovelService'
import { Novel, Chapter, BackupHistory } from '../../shared/types'

export class StorageService {
  private db: DatabaseService
  private novelService: NovelService

  constructor(db: DatabaseService) {
    this.db = db
    this.novelService = new NovelService(db)
  }

  async saveFile(data: string, filePath: string): Promise<boolean> {
    try {
      const dir = path.dirname(filePath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
      
      fs.writeFileSync(filePath, data)
      return true
    } catch {
      return false
    }
  }

  async readFile(filePath: string): Promise<string | null> {
    try {
      if (!fs.existsSync(filePath)) {
        return null
      }
      
      const content = fs.readFileSync(filePath, 'utf-8')
      return content
    } catch {
      return null
    }
  }

  async exportNovel(novelId: string, options: { format: string, includeMetadata?: boolean }): Promise<string> {
    try {
      const novel = await this.novelService.getNovel(novelId)
      if (!novel) {
        throw new Error('Novel not found')
      }

      const chapters = await this.novelService.getChaptersByNovelId(novelId)
      
      let content = ''
      
      switch (options.format) {
        case 'markdown':
          content = this.generateMarkdown(novel, chapters, options)
          break
        case 'txt':
          content = this.generateTxt(novel, chapters, options)
          break
        case 'doc':
          content = this.generateDoc(novel, chapters, options)
          break
        default:
          throw new Error(`Unsupported format: ${options.format}`)
      }

      const fileName = `${novel.title}.${options.format}`
      const filePath = path.join(app.getPath('downloads'), fileName)
      
      await this.saveFile(content, filePath)
      return filePath
    } catch (error) {
      throw error
    }
  }

  private generateMarkdown(novel: Novel, chapters: Chapter[], options: { includeMetadata?: boolean }): string {
    let content = `# ${novel.title}\n\n`
    
    if (novel.author) {
      content += `**作者**: ${novel.author}\n\n`
    }
    
    if (novel.description) {
      content += `## 简介\n\n${novel.description}\n\n`
    }

    if (options.includeMetadata) {
      content += `## 元数据\n\n`
      content += `- **字数**: ${novel.wordCount}\n`
      content += `- **章节数**: ${novel.chapterCount}\n`
      content += `- **状态**: ${novel.status}\n`
      content += `- **类型**: ${novel.genre.join(', ')}\n\n`
    }

    content += `## 正文\n\n`
    
    chapters.forEach(chapter => {
      content += `### ${chapter.title}\n\n`
      content += `${chapter.content}\n\n`
      content += `---\n\n`
    })

    return content
  }

  private generateTxt(novel: Novel, chapters: Chapter[], options: { includeMetadata?: boolean }): string {
    let content = `${novel.title}\n`
    content += '='.repeat(novel.title.length) + '\n\n'
    
    if (novel.author) {
      content += `作者: ${novel.author}\n\n`
    }
    
    if (novel.description) {
      content += `简介:\n${novel.description}\n\n`
    }

    if (options.includeMetadata) {
      content += '元数据:\n'
      content += `字数: ${novel.wordCount}\n`
      content += `章节数: ${novel.chapterCount}\n`
      content += `状态: ${novel.status}\n`
      content += `类型: ${novel.genre.join(', ')}\n\n`
    }

    content += '正文:\n\n'
    
    chapters.forEach(chapter => {
      content += `${chapter.title}\n`
      content += '-'.repeat(chapter.title.length) + '\n\n'
      content += `${chapter.content}\n\n`
      content += '\n'
    })

    return content
  }

  private generateDoc(novel: Novel, chapters: Chapter[], options: { includeMetadata?: boolean }): string {
    // For now, return HTML content that can be opened as a document
    let content = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${novel.title}</title>
    <style>
        body { font-family: 'Times New Roman', serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { text-align: center; }
        h2 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        h3 { color: #666; }
        .metadata { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .chapter { margin-bottom: 30px; }
        .chapter-content { text-align: justify; }
    </style>
</head>
<body>
    <h1>${novel.title}</h1>`
    
    if (novel.author) {
      content += `<p style="text-align: center; font-style: italic;">作者: ${novel.author}</p>`
    }
    
    if (novel.description) {
      content += `<h2>简介</h2><p>${novel.description}</p>`
    }

    if (options.includeMetadata) {
      content += `<div class="metadata">
        <h2>元数据</h2>
        <p><strong>字数:</strong> ${novel.wordCount}</p>
        <p><strong>章节数:</strong> ${novel.chapterCount}</p>
        <p><strong>状态:</strong> ${novel.status}</p>
        <p><strong>类型:</strong> ${novel.genre.join(', ')}</p>
      </div>`
    }

    content += '<h2>正文</h2>'
    
    chapters.forEach(chapter => {
      content += `<div class="chapter">
        <h3>${chapter.title}</h3>
        <div class="chapter-content">${chapter.content.replace(/\n/g, '<br>')}</div>
      </div>`
    })

    content += '</body></html>'
    
    return content
  }

  async createBackup(novelId: string): Promise<string> {
    try {
      const novel = await this.novelService.getNovel(novelId)
      if (!novel) {
        throw new Error('Novel not found')
      }

      const chapters = await this.novelService.getChaptersByNovelId(novelId)
      const characters = await this.novelService.getCharactersByNovelId(novelId)
      const materials = await this.novelService.getMaterialsByNovelId(novelId)
      const outlines = await this.novelService.getOutlinesByNovelId(novelId)

      const backupData = {
        novel,
        chapters,
        characters,
        materials,
        outlines,
        backupDate: new Date().toISOString(),
        version: '1.0'
      }

      const backupDir = path.join(app.getPath('userData'), 'backups')
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true })
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const fileName = `${novel.title}_${timestamp}.backup`
      const filePath = path.join(backupDir, fileName)

      await this.saveFile(JSON.stringify(backupData, null, 2), filePath)
      
      // Save backup record to database
      const stats = fs.statSync(filePath)
      await this.db.run(
        'INSERT INTO backup_history (id, novel_id, backup_path, backup_size) VALUES (?, ?, ?, ?)',
        [Date.now().toString(), novelId, filePath, stats.size]
      )

      return filePath
    } catch (error) {
      throw error
    }
  }

  async restoreBackup(backupPath: string): Promise<void> {
    try {
      const backupContent = await this.readFile(backupPath)
      if (!backupContent) {
        throw new Error('Backup file not found or corrupted')
      }
      const backupData = JSON.parse(backupContent)

      // Restore novel
      await this.novelService.createNovel(backupData.novel)

      // Restore chapters
      for (const chapter of backupData.chapters) {
        await this.novelService.createChapter(chapter)
      }

      // Restore characters
      for (const character of backupData.characters) {
        await this.novelService.createCharacter(character)
      }

      // Restore materials
      for (const material of backupData.materials) {
        await this.novelService.createMaterial(material)
      }

      // Restore outlines
      for (const outline of backupData.outlines) {
        await this.novelService.createOutline(outline)
      }
    } catch (error) {
      throw error
    }
  }

  async getBackupHistory(novelId: string): Promise<BackupHistory[]> {
    try {
      const rows = await this.db.findAll('backup_history', 'novel_id = ? ORDER BY created_at DESC', [novelId])
      return rows.map(row => ({
        id: row.id as string,
        novelId: row.novel_id as string,
        backupPath: row.backup_path as string,
        backupSize: row.backup_size as number,
        createdAt: new Date(row.created_at as string)
      }))
    } catch {
      return []
    }
  }

  async deleteBackup(backupId: string): Promise<void> {
    try {
      const backup = await this.db.get('SELECT backup_path FROM backup_history WHERE id = ?', [backupId])
      if (backup && backup.backup_path) {
        if (fs.existsSync(backup.backup_path as string)) {
          fs.unlinkSync(backup.backup_path as string)
        }
        await this.db.delete('backup_history', backupId)
      }
    } catch (error) {
      throw error
    }
  }
}
