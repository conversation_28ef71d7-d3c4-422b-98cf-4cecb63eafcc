import { BaseAIProvider } from './BaseAIProvider'
import { AIRequest, AIResponse } from '../../shared/types'

export class QwenProvider extends BaseAIProvider {
  constructor() {
    super(
      process.env.QWEN_API_KEY || '',
      'https://dashscope.aliyuncs.com/api/v1',
      'qwen-turbo'
    )
  }

  async generate(request: AIRequest): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new Error('Qwen API key is not configured')
    }

    // 阿里云DashScope API格式略有不同
    const response = await fetch(`${this.baseUrl}/services/aigc/text-generation/generation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: this.model,
        input: {
          messages: [
            {
              role: 'user',
              content: request.prompt
            }
          ]
        },
        parameters: {
          max_tokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.7,
          stream: false
        }
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return {
      content: data.output?.text || '',
      provider: this.getName(),
      model: this.model,
      usage: data.usage
    }
  }

  isAvailable(): boolean {
    return this.validateApiKey()
  }

  getName(): string {
    return 'qwen'
  }
}