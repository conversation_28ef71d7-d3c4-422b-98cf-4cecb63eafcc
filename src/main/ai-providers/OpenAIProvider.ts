import { BaseAIProvider } from './BaseAIProvider'
import { AIRequest, AIResponse } from '../../shared/types'

export class OpenAIProvider extends BaseAIProvider {
  constructor() {
    super(
      process.env.OPENAI_API_KEY || '',
      'https://api.openai.com/v1',
      'gpt-3.5-turbo'
    )
  }

  async generate(request: AIRequest): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new Error('OpenAI API key is not configured')
    }

    return this.makeRequest(request, '/chat/completions')
  }

  isAvailable(): boolean {
    return this.validateApiKey()
  }

  getName(): string {
    return 'openai'
  }
}