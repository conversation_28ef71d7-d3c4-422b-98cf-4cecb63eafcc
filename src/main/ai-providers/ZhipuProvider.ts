import { BaseAIProvider } from './BaseAIProvider'
import { AIRequest, AIResponse } from '../../shared/types'

export class ZhipuProvider extends BaseAIProvider {
  constructor() {
    super(
      process.env.ZHIPU_API_KEY || '',
      'https://open.bigmodel.cn/api/paas/v4',
      'glm-4'
    )
  }

  async generate(request: AIRequest): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new Error('Zhipu AI API key is not configured')
    }

    return this.makeRequest(request, '/chat/completions')
  }

  isAvailable(): boolean {
    return this.validateApiKey()
  }

  getName(): string {
    return 'zhipu'
  }
}