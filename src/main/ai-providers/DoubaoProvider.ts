import { BaseAIProvider } from './BaseAIProvider'
import { AIRequest, AIResponse } from '../../shared/types'

export class DoubaoProvider extends BaseAIProvider {
  constructor() {
    super(
      process.env.DOUBAO_API_KEY || '',
      'https://ark.cn-beijing.volces.com/api/v3',
      'doubao-pro-4k'
    )
  }

  async generate(request: AIRequest): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new Error('Doubao API key is not configured')
    }

    return this.makeRequest(request, '/chat/completions')
  }

  isAvailable(): boolean {
    return this.validateApiKey()
  }

  getName(): string {
    return 'doubao'
  }
}