import { AIRequest, AIResponse } from '../../shared/types'

export abstract class BaseAIProvider {
  protected apiKey: string
  protected baseUrl: string
  protected model: string

  constructor(apiKey: string, baseUrl: string, model: string) {
    this.apiKey = apiKey
    this.baseUrl = baseUrl
    this.model = model
  }

  abstract generate(request: AIRequest): Promise<AIResponse>
  abstract isAvailable(): boolean
  abstract getName(): string

  protected async makeRequest(request: AIRequest, endpoint: string): Promise<AIResponse> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: 'user',
            content: request.prompt
          }
        ],
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: false
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return {
      content: data.choices[0]?.message?.content || '',
      provider: this.getName(),
      model: this.model,
      usage: data.usage
    }
  }

  protected validateApiKey(): boolean {
    return this.apiKey && this.apiKey.length > 0
  }
}