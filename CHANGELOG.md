# 更新日志

本文档记录了小说创作管理器的所有重要更改。格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)。

## [1.0.0] - 2024-08-06

### 新增 (Added)
- **核心写作功能**
  - 智能文本编辑器，支持富文本格式
  - 实时自动保存功能，防止数据丢失
  - 章节管理系统，支持章节排序和导航
  - 字数统计和写作进度跟踪

- **AI助手功能**
  - 智能文本续写，基于上下文生成内容
  - 文本润色功能，改善语法和表达
  - 剧情推荐系统，提供情节发展建议
  - 角色关系分析，可视化角色网络
  - 多AI提供商支持（OpenAI、智谱AI、通义千问、豆包）

- **数据管理**
  - 本地SQLite数据库，无需外部数据库
  - 小说项目管理，支持多小说同时管理
  - 角色管理系统，包含详细角色信息
  - 素材库功能，支持多种素材类型
  - 大纲构建工具，支持层级结构

- **导出功能**
  - 多格式导出支持（Markdown、TXT、HTML）
  - 批量导出功能
  - 自定义导出选项
  - 元数据包含功能

- **用户界面**
  - 现代化响应式界面设计
  - 深色/浅色主题支持
  - 可自定义的工作区布局
  - 专注模式，减少干扰
  - 直观的侧边栏导航

### 技术特性 (Technical Features)
- **跨平台支持**: Windows、macOS、Linux 全平台支持
- **现代技术栈**: React 18 + TypeScript + Electron 28
- **性能优化**: 基于 Vite 的快速构建和热重载
- **类型安全**: 完整的 TypeScript 类型系统
- **模块化架构**: 清晰的代码结构和模块分离
- **数据安全**: 本地数据存储，隐私保护

### 系统要求 (System Requirements)
- **操作系统**: Windows 10+, macOS 10.14+, Linux
- **Node.js**: 18.0+ (开发环境)
- **内存**: 最少 4GB RAM，推荐 8GB+
- **存储**: 最少 2GB 可用空间

## [0.9.0] - 2024-07-15

### 新增 (Added)
- 初始版本发布
- 基础编辑器功能
- 简单的文本保存功能
- 基本的用户界面

### 变更 (Changed)
- 项目架构设计
- 技术选型确定
- 开发环境搭建

## [计划中] (Planned)

### [1.1.0] - 计划发布
- **云同步功能**: 支持多设备数据同步
- **协作功能**: 多人协作编辑
- **版本控制**: 完整的文档版本历史
- **更多AI功能**: AI驱动的写作建议和优化
- **移动端支持**: iOS和Android应用
- **插件系统**: 支持第三方插件扩展

### [1.2.0] - 远期计划
- **社区功能**: 用户社区和作品分享
- **出版工具**: 直接发布到电子书平台
- **高级AI功能**: 更智能的写作助手
- **数据分析**: 写作习惯和进度分析
- **多语言支持**: 界面多语言化

---

## 版本说明

### 版本号格式
项目使用 [语义化版本](https://semver.org/) 格式：
- **主版本号**: 不兼容的API更改
- **次版本号**: 向下兼容的功能新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增 (Added)**: 新功能
- **变更 (Changed)**: 已有功能的变更
- **废弃 (Deprecated)**: 即将移除的功能
- **移除 (Removed)**: 已移除的功能
- **修复 (Fixed)**: 问题修正
- **安全 (Security)**: 安全相关的修复

### 更新频率
- **主版本**: 每6-12个月发布一次
- **次版本**: 每2-3个月发布一次
- **修订版本**: 根据需要随时发布

---

## 贡献

欢迎通过 [GitHub Issues](https://github.com/your-username/novel-creation-manager/issues) 报告问题或建议新功能。

## 支持

如果您需要帮助，请：
1. 查看 [用户手册](docs/user-manual.md)
2. 浏览 [常见问题](docs/faq.md)
3. 在 [GitHub Discussions](https://github.com/your-username/novel-creation-manager/discussions) 中寻求帮助
4. 联系技术支持: <EMAIL>