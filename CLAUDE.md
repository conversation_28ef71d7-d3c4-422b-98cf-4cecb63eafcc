# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Novel Creation Manager** (小说创作管理器) - a comprehensive intelligent writing assistant designed specifically for novelists and creative writers. The application provides an integrated platform for story planning, writing, editing, and publishing with AI-powered features to enhance creativity and productivity.

## Architecture Overview

The project follows a modern web application architecture with:

- **Frontend**: Responsive web interface using React or Vue.js for multi-device compatibility
- **Backend**: Microservices architecture with modular API design for scalability and performance
- **Data Storage**: Hybrid approach using both relational and non-relational databases
- **AI Integration**: Machine learning models for intelligent writing assistance
- **Export Engine**: Multi-format document conversion system

## Code Quality Standards

### File Size Limits
- **Python/JavaScript/TypeScript**: Maximum 200 lines per file
- **Java/Go/Rust**: Maximum 250 lines per file
- **Directory Structure**: Maximum 8 files per directory level (use subdirectories when exceeding)

### Anti-Patterns to Avoid
**Rigidity**: System should be flexible for easy changes
**Redundancy**: Eliminate duplicate code logic
**Circular Dependencies**: Maintain clean module separation
**Fragility**: Ensure changes don't break unrelated functionality
**Obscurity**: Write clear, self-documenting code
**Data Clumps**: Group related data into objects
**Needless Complexity**: Use appropriate solutions for problem scale

## Core Features Architecture

### Writing & Editing
- **Outline Builder**: Modular story structure planning
- **Text Editor**: Rich formatting support for long-form writing
- **Real-time Auto-save**: Prevents data loss
- **Distraction-free Mode**: Focused writing environment

### AI-Powered Features
- **Intelligent Continuation**: Context-aware story continuation
- **Plot Recommendation**: AI-driven plot development suggestions
- **Text Refinement**: Style-aware content polishing
- **Character Relationship Mapping**: Visual character network analysis
- **Timeline Management**: Story chronology tracking

### Data Management
- **Material Library**: Organized storage for creative assets
- **Version Control**: Document history and backup management
- **Multi-format Export**: DOC, Markdown, TXT conversion
- **Progress Tracking**: Word count and chapter statistics

## Development Guidelines

### Technology Stack
- **Frontend Framework**: React or Vue.js with responsive design
- **Backend**: Microservices with RESTful APIs
- **Database**: Hybrid SQL/NoSQL approach
- **AI/ML**: Machine learning models for text processing
- **Export Libraries**: Document format conversion tools

### Module Organization
```
src/
├── frontend/           # Web interface components
├── backend/           # API services and business logic
├── ai-services/       # Machine learning models
├── data-processing/   # Export and conversion engines
├── storage/          # Database and file management
└── utils/            # Shared utilities
```

### Code Review Focus
When reviewing code, specifically check for:
- File size compliance (200/250 line limits)
- Directory structure organization (8 files max per level)
- Architecture anti-patterns
- Proper separation of concerns
- Clear documentation and intent

## AI Integration Requirements

### Text Processing Features
- **Error Correction**: Automated typo detection and correction
- **Context Analysis**: Understanding story context for recommendations
- **Style Adaptation**: Maintain consistent character voices and writing style
- **Plot Coherence**: Ensure logical story progression

### Visual Tools
- **Character Relationship Graphs**: Interactive relationship visualization
- **World Building Maps**: Geographic and political landscape mapping
- **Timeline Visualization**: Story event chronology display

## Security & Data Protection

### Privacy Considerations
- User content encryption for sensitive writing materials
- Secure backup mechanisms with version control
- Local storage options for offline work
- User-controlled data sharing settings

## Performance Requirements

### Real-time Features
- Auto-save functionality with minimal latency
- Instant AI suggestions during writing
- Responsive interface for large documents
- Efficient search across extensive libraries

## Development Commands

### Setup Commands
- Install dependencies: `npm install` (frontend) / `pip install -r requirements.txt` (backend)
- Start development server: `npm run dev` (frontend) / appropriate backend command
- Run tests: `npm test` or `pytest`
- Build production: `npm run build`

### Code Quality
- Linting: `npm run lint` or appropriate language linter
- Format: `npm run format` or language-specific formatter
- Type checking: TypeScript `tsc` or Python `mypy`

## Testing Strategy

### Core Testing Areas
- **AI Model Accuracy**: Test recommendation and continuation quality
- **Export Functionality**: Verify format conversion accuracy
- **Real-time Features**: Test auto-save and synchronization
- **Performance**: Test with large documents and libraries
- **Security**: Test data protection and privacy features

## Deployment Considerations

### Scalability Requirements
- Handle multiple concurrent users and large documents
- Efficient AI model serving for real-time suggestions
- Robust backup and recovery systems
- Multi-format export processing capabilities