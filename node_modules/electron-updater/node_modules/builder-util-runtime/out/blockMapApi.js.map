{"version": 3, "file": "blockMapApi.js", "sourceRoot": "", "sources": ["../src/blockMapApi.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface FileChunks {\n  checksums: Array<string>\n  sizes: Array<number>\n}\n\nexport interface BlockMap {\n  version: \"1\" | \"2\"\n  files: Array<BlockMapFile>\n}\n\nexport interface BlockMapFile extends FileChunks {\n  name: string\n  offset: number\n}\n"]}