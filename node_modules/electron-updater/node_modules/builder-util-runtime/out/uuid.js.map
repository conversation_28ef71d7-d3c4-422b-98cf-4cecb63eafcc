{"version": 3, "file": "uuid.js", "sourceRoot": "", "sources": ["../src/uuid.ts"], "names": [], "mappings": ";;;AAAA,mCAAgD;AAChD,mCAAkC;AAElC,MAAM,WAAW,GAAG,kDAAkD,CAAA;AAEtE,2CAA2C;AAC3C,MAAM,UAAU,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAA;AAClC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;AAEpC,2BAA2B;AAC3B,MAAM,QAAQ,GAAQ,EAAE,CAAA;AAExB,2BAA2B;AAC3B,MAAM,QAAQ,GAAkB,EAAE,CAAA;AAClC,yBAAyB;AACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7B,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC9C,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACjB,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;AACnB,CAAC;AAED,aAAa;AACb,MAAa,IAAI;IAQf,YAAY,IAAqB;QAPzB,UAAK,GAAkB,IAAI,CAAA;QAClB,WAAM,GAAkB,IAAI,CAAA;QAO3C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;QAC/B,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAQ,CAAA;QAE7B,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAc,CAAA;QAC7B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,IAAc,CAAA;QAC9B,CAAC;IACH,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,IAAqB,EAAE,SAAiB;QAChD,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACjD,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,MAAO,CAAC,CAAA;QACtC,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,OAAO;QACL,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAA;IACnD,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,IAAqB,EAAE,MAAM,GAAG,CAAC;QAC5C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;YAEzB,IAAI,CAAC,+CAA+C,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChE,OAAO,KAAK,CAAA;YACd,CAAC;YAED,IAAI,IAAI,KAAK,sCAAsC,EAAE,CAAC;gBACpD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAA;YAChE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBACpD,OAAO,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChE,MAAM,EAAE,OAAO;aAChB,CAAA;QACH,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,EAAE,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAA;YACd,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,CAAA;YACT,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC3B,MAAK;gBACP,CAAC;YACH,CAAC;YACD,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;gBACb,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAA;YACjE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBACvC,OAAO,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,EAAE,QAAQ;aACjB,CAAA;QACH,CAAC;QAED,MAAM,IAAA,gBAAQ,EAAC,sBAAsB,EAAE,uBAAuB,CAAC,CAAA;IACjE,CAAC;IAED,sCAAsC;IACtC,MAAM,CAAC,KAAK,CAAC,KAAa;QACxB,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QACrC,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,CAAC,IAAI,CAAC,CAAA;YACR,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;;AA7FH,oBA8FC;AAzFC,0BAA0B;AACV,QAAG,GAAW,IAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,AAA7D,CAA6D;AA0FlF,qCAAqC;AACrC,SAAS,UAAU,CAAC,IAAY;IAC9B,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,CAAC,CAAC;QACP,KAAK,CAAC,CAAC;QACP,KAAK,CAAC;YACJ,OAAO,KAAK,CAAA;QACd,KAAK,CAAC,CAAC;QACP,KAAK,CAAC;YACJ,OAAO,SAAS,CAAA;QAClB,KAAK,CAAC;YACJ,OAAO,WAAW,CAAA;QACpB;YACE,OAAO,QAAQ,CAAA;IACnB,CAAC;AACH,CAAC;AAED,IAAK,YAIJ;AAJD,WAAK,YAAY;IACf,iDAAK,CAAA;IACL,mDAAM,CAAA;IACN,mDAAM,CAAA;AACR,CAAC,EAJI,YAAY,KAAZ,YAAY,QAIhB;AAED,UAAU;AACV,SAAS,SAAS,CAAC,IAAqB,EAAE,UAAkB,EAAE,OAAe,EAAE,SAAiB,EAAE,WAAyB,YAAY,CAAC,KAAK;IAC3I,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,UAAU,CAAC,CAAA;IAEnC,MAAM,gBAAgB,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAA;IACjD,IAAI,gBAAgB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/C,MAAM,IAAA,gBAAQ,EAAC,WAAW,EAAE,uBAAuB,CAAC,CAAA;IACtD,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAEjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;IAC5B,IAAI,MAAW,CAAA;IACf,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,YAAY,CAAC,MAAM;YACtB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,CAAA;YACxC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAA;YACrC,MAAM,GAAG,MAAM,CAAA;YACf,MAAK;QACP,KAAK,YAAY,CAAC,MAAM;YACtB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,CAAA;YACxC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAA;YACrC,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAA;YACzB,MAAK;QACP;YACE,MAAM;gBACJ,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,GAAG;oBACH,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,GAAG;oBACH,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;oBACtC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,GAAG;oBACH,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;oBACnC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,GAAG;oBACH,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YACtB,MAAK;IACT,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,SAAS,CAAC,MAAc;IAC/B,OAAO,CACL,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,GAAG;QACH,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,GAAG;QACH,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,GAAG;QACH,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,GAAG;QACH,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CACrB,CAAA;AACH,CAAC;AAED,qCAAqC;AACxB,QAAA,GAAG,GAAG,IAAI,IAAI,CAAC,sCAAsC,CAAC,CAAA;AAEnE,uBAAuB;AAEvB,+BAA+B;AAE/B,0CAA0C;AAC1C,uDAAuD;AACvD,IAAI", "sourcesContent": ["import { createHash, randomBytes } from \"crypto\"\nimport { newError } from \"./error\"\n\nconst invalidName = \"options.name must be either a string or a Buffer\"\n\n// Node ID according to rfc4122#section-4.5\nconst randomHost = randomBytes(16)\nrandomHost[0] = randomHost[0] | 0x01\n\n// lookup table hex to byte\nconst hex2byte: any = {}\n\n// lookup table byte to hex\nconst byte2hex: Array<string> = []\n// populate lookup tables\nfor (let i = 0; i < 256; i++) {\n  const hex = (i + 0x100).toString(16).substr(1)\n  hex2byte[hex] = i\n  byte2hex[i] = hex\n}\n\n// UUID class\nexport class UUID {\n  private ascii: string | null = null\n  private readonly binary: Buffer | null = null\n  private readonly version: number\n\n  // from rfc4122#appendix-C\n  static readonly OID: Buffer = UUID.parse(\"6ba7b812-9dad-11d1-80b4-00c04fd430c8\")\n\n  constructor(uuid: Buffer | string) {\n    const check = UUID.check(uuid)\n    if (!check) {\n      throw new Error(\"not a UUID\")\n    }\n\n    this.version = check.version!\n\n    if (check.format === \"ascii\") {\n      this.ascii = uuid as string\n    } else {\n      this.binary = uuid as Buffer\n    }\n  }\n\n  static v5(name: string | Buffer, namespace: Buffer) {\n    return uuidNamed(name, \"sha1\", 0x50, namespace)\n  }\n\n  toString() {\n    if (this.ascii == null) {\n      this.ascii = stringify(this.binary!)\n    }\n    return this.ascii\n  }\n\n  inspect() {\n    return `UUID v${this.version} ${this.toString()}`\n  }\n\n  static check(uuid: Buffer | string, offset = 0) {\n    if (typeof uuid === \"string\") {\n      uuid = uuid.toLowerCase()\n\n      if (!/^[a-f0-9]{8}(-[a-f0-9]{4}){3}-([a-f0-9]{12})$/.test(uuid)) {\n        return false\n      }\n\n      if (uuid === \"00000000-0000-0000-0000-000000000000\") {\n        return { version: undefined, variant: \"nil\", format: \"ascii\" }\n      }\n\n      return {\n        version: (hex2byte[uuid[14] + uuid[15]] & 0xf0) >> 4,\n        variant: getVariant((hex2byte[uuid[19] + uuid[20]] & 0xe0) >> 5),\n        format: \"ascii\",\n      }\n    }\n\n    if (Buffer.isBuffer(uuid)) {\n      if (uuid.length < offset + 16) {\n        return false\n      }\n\n      let i = 0\n      for (; i < 16; i++) {\n        if (uuid[offset + i] !== 0) {\n          break\n        }\n      }\n      if (i === 16) {\n        return { version: undefined, variant: \"nil\", format: \"binary\" }\n      }\n\n      return {\n        version: (uuid[offset + 6] & 0xf0) >> 4,\n        variant: getVariant((uuid[offset + 8] & 0xe0) >> 5),\n        format: \"binary\",\n      }\n    }\n\n    throw newError(\"Unknown type of uuid\", \"ERR_UNKNOWN_UUID_TYPE\")\n  }\n\n  // read stringified uuid into a Buffer\n  static parse(input: string): Buffer {\n    const buffer = Buffer.allocUnsafe(16)\n    let j = 0\n    for (let i = 0; i < 16; i++) {\n      buffer[i] = hex2byte[input[j++] + input[j++]]\n      if (i === 3 || i === 5 || i === 7 || i === 9) {\n        j += 1\n      }\n    }\n    return buffer\n  }\n}\n\n// according to rfc4122#section-4.1.1\nfunction getVariant(bits: number) {\n  switch (bits) {\n    case 0:\n    case 1:\n    case 3:\n      return \"ncs\"\n    case 4:\n    case 5:\n      return \"rfc4122\"\n    case 6:\n      return \"microsoft\"\n    default:\n      return \"future\"\n  }\n}\n\nenum UuidEncoding {\n  ASCII,\n  BINARY,\n  OBJECT,\n}\n\n// v3 + v5\nfunction uuidNamed(name: string | Buffer, hashMethod: string, version: number, namespace: Buffer, encoding: UuidEncoding = UuidEncoding.ASCII) {\n  const hash = createHash(hashMethod)\n\n  const nameIsNotAString = typeof name !== \"string\"\n  if (nameIsNotAString && !Buffer.isBuffer(name)) {\n    throw newError(invalidName, \"ERR_INVALID_UUID_NAME\")\n  }\n\n  hash.update(namespace)\n  hash.update(name)\n\n  const buffer = hash.digest()\n  let result: any\n  switch (encoding) {\n    case UuidEncoding.BINARY:\n      buffer[6] = (buffer[6] & 0x0f) | version\n      buffer[8] = (buffer[8] & 0x3f) | 0x80\n      result = buffer\n      break\n    case UuidEncoding.OBJECT:\n      buffer[6] = (buffer[6] & 0x0f) | version\n      buffer[8] = (buffer[8] & 0x3f) | 0x80\n      result = new UUID(buffer)\n      break\n    default:\n      result =\n        byte2hex[buffer[0]] +\n        byte2hex[buffer[1]] +\n        byte2hex[buffer[2]] +\n        byte2hex[buffer[3]] +\n        \"-\" +\n        byte2hex[buffer[4]] +\n        byte2hex[buffer[5]] +\n        \"-\" +\n        byte2hex[(buffer[6] & 0x0f) | version] +\n        byte2hex[buffer[7]] +\n        \"-\" +\n        byte2hex[(buffer[8] & 0x3f) | 0x80] +\n        byte2hex[buffer[9]] +\n        \"-\" +\n        byte2hex[buffer[10]] +\n        byte2hex[buffer[11]] +\n        byte2hex[buffer[12]] +\n        byte2hex[buffer[13]] +\n        byte2hex[buffer[14]] +\n        byte2hex[buffer[15]]\n      break\n  }\n  return result\n}\n\nfunction stringify(buffer: Buffer) {\n  return (\n    byte2hex[buffer[0]] +\n    byte2hex[buffer[1]] +\n    byte2hex[buffer[2]] +\n    byte2hex[buffer[3]] +\n    \"-\" +\n    byte2hex[buffer[4]] +\n    byte2hex[buffer[5]] +\n    \"-\" +\n    byte2hex[buffer[6]] +\n    byte2hex[buffer[7]] +\n    \"-\" +\n    byte2hex[buffer[8]] +\n    byte2hex[buffer[9]] +\n    \"-\" +\n    byte2hex[buffer[10]] +\n    byte2hex[buffer[11]] +\n    byte2hex[buffer[12]] +\n    byte2hex[buffer[13]] +\n    byte2hex[buffer[14]] +\n    byte2hex[buffer[15]]\n  )\n}\n\n// according to rfc4122#section-4.1.7\nexport const nil = new UUID(\"00000000-0000-0000-0000-000000000000\")\n\n// UUID.v4 = uuidRandom\n\n// UUID.v4fast = uuidRandomFast\n\n// UUID.v3 = function(options, callback) {\n//     return uuidNamed(\"md5\", 0x30, options, callback)\n// }\n"]}