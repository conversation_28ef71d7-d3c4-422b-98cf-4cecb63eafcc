{"version": 3, "file": "updateInfo.js", "sourceRoot": "", "sources": ["../src/updateInfo.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface ReleaseNoteInfo {\n  /**\n   * The version.\n   */\n  readonly version: string\n\n  /**\n   * The note.\n   */\n  readonly note: string | null\n}\n\nexport interface BlockMapDataHolder {\n  /**\n   * The file size. Used to verify downloaded size (save one HTTP request to get length).\n   * Also used when block map data is embedded into the file (appimage, windows web installer package).\n   */\n  size?: number\n\n  /**\n   * The block map file size. Used when block map data is embedded into the file (appimage, windows web installer package).\n   * This information can be obtained from the file itself, but it requires additional HTTP request,\n   * so, to reduce request count, block map size is specified in the update metadata too.\n   */\n  blockMapSize?: number\n\n  /**\n   * The file checksum.\n   */\n  readonly sha512: string\n\n  readonly isAdminRightsRequired?: boolean\n}\n\nexport interface PackageFileInfo extends BlockMapDataHolder {\n  readonly path: string\n}\n\nexport interface UpdateFileInfo extends BlockMapDataHolder {\n  url: string\n}\n\nexport interface UpdateInfo {\n  /**\n   * The version.\n   */\n  readonly version: string\n\n  readonly files: Array<UpdateFileInfo>\n\n  /** @deprecated */\n  readonly path: string\n\n  /** @deprecated */\n  readonly sha512: string\n\n  /**\n   * The release name.\n   */\n  releaseName?: string | null\n\n  /**\n   * The release notes. List if `updater.fullChangelog` is set to `true`, `string` otherwise.\n   */\n  releaseNotes?: string | Array<ReleaseNoteInfo> | null\n\n  /**\n   * The release date.\n   */\n  releaseDate: string\n\n  /**\n   * The [staged rollout](./auto-update.md#staged-rollouts) percentage, 0-100.\n   */\n  readonly stagingPercentage?: number\n\n  /**\n   * The minimum version of system required for the app to run. Sample value: macOS `23.1.0`, Windows `10.0.22631`.\n   * Same with os.release() value, this is a kernel version.\n   */\n  readonly minimumSystemVersion?: string\n}\n\nexport interface WindowsUpdateInfo extends UpdateInfo {\n  packages?: { [arch: string]: PackageFileInfo } | null\n\n  /**\n   * @deprecated\n   * @private\n   */\n  sha2?: string\n}\n"]}