{"name": "ast-v8-to-istanbul", "version": "0.3.4", "description": "AST-aware v8-to-istanbul", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "type": "module", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "homepage": "https://github.com/AriPerkkio/ast-v8-to-istanbul", "bugs": "https://github.com/AriPerkkio/ast-v8-to-istanbul", "repository": {"type": "git", "url": "git+https://github.com/AriPerkkio/ast-v8-to-istanbul.git"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.29", "estree-walker": "^3.0.3", "js-tokens": "^9.0.1"}, "devDependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.2", "@eslint/js": "^9.32.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@types/estree": "^1.0.8", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-lib-instrument": "^1.7.7", "@types/istanbul-lib-report": "^3.0.3", "@types/istanbul-lib-source-maps": "^4.0.4", "@types/istanbul-reports": "^3.0.4", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/coverage-istanbul": "^4.0.0-beta.4", "@vitest/coverage-v8": "^4.0.0-beta.4", "acorn": "^8.15.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import-x": "^4.16.1", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-unicorn": "^60.0.0", "gh-pages": "^6.3.0", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-instrument": "^6.0.3", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^5.0.6", "istanbul-reports": "^3.1.7", "magic-string": "^0.30.17", "oxc-parser": "^0.78.0", "prettier": "^3.6.2", "rollup": "^4.46.1", "tinyrainbow": "^2.0.0", "tsdown": "^0.13.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.6", "vitest": "^4.0.0-beta.4", "vue": "^3.5.18", "yaml": "^2.8.0"}, "prettier": {}, "scripts": {"dev": "tsdown --watch --sourcemap", "bench": "node --experimental-strip-types --cpu-prof --cpu-prof-dir=./bench-profile ./benchmark/run.ts", "bench:ci": "node --experimental-strip-types ./benchmark/run.ts", "build": "tsdown", "deploy": "touch coverage/.nojekyll && gh-pages --dist coverage --dotfiles", "test": "vitest", "test:dev": "vitest --project 'vite/parseAstAsync'", "lint": "eslint --max-warnings 0", "typecheck": "tsc --noEmit", "debug": "open fixture-coverage/index.html istanbul-coverage/index.html", "verify": "pnpm build; pnpm lint; pnpm typecheck; pnpm run test --run; pnpm bench"}}