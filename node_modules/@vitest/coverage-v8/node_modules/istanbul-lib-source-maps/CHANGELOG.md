# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [4.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0) (2019-12-20)

**Note:** Version bump only for package istanbul-lib-source-maps





# [4.0.0-alpha.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0-alpha.5) (2019-12-07)

**Note:** Version bump only for package istanbul-lib-source-maps





# [4.0.0-alpha.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0-alpha.4) (2019-11-16)


### Bug Fixes

* sourceFinder cannot be async. ([#501](https://github.com/istanbuljs/istanbuljs/issues/501)) ([094f1b8](https://github.com/istanbuljs/istanbuljs/commit/094f1b83b4652c5ba492781620cb6358c685a849))





# [4.0.0-alpha.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0-alpha.3) (2019-11-15)


### Bug Fixes

* mappedCoverage.addStatement is not a function ([#500](https://github.com/istanbuljs/istanbuljs/issues/500)) ([d77cc14](https://github.com/istanbuljs/istanbuljs/commit/d77cc147f7d791686af2975f7d906603335d0bfc)), closes [istanbuljs/nyc#940](https://github.com/istanbuljs/nyc/issues/940)





# [4.0.0-alpha.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0-alpha.2) (2019-10-09)


### Features

* Convert to async API ([#489](https://github.com/istanbuljs/istanbuljs/issues/489)) ([f8ebbc9](https://github.com/istanbuljs/istanbuljs/commit/f8ebbc9))


### BREAKING CHANGES

* MapStore#transformCoverage is now async and returns a
the coverage data only.  The `sourceFinder` method is now async and
provided directly on the `MapStore` instance.





# [4.0.0-alpha.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0-alpha.1) (2019-10-06)


### Bug Fixes

* **package:** update rimraf to version 3.0.0 ([b6e7953](https://github.com/istanbuljs/istanbuljs/commit/b6e7953))


### Features

* Accept SourceStore and sourceStoreOpts options ([#482](https://github.com/istanbuljs/istanbuljs/issues/482)) ([0dc45a6](https://github.com/istanbuljs/istanbuljs/commit/0dc45a6))
* Add addInputSourceMapsSync and getSourceMapSync methods ([#484](https://github.com/istanbuljs/istanbuljs/issues/484)) ([dd7048e](https://github.com/istanbuljs/istanbuljs/commit/dd7048e))


### BREAKING CHANGES

* sourceStore and tmpdir options are removed.





# [4.0.0-alpha.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@4.0.0-alpha.0) (2019-06-19)


### Features

* Update dependencies, require Node.js 8 ([#401](https://github.com/istanbuljs/istanbuljs/issues/401)) ([bf3a539](https://github.com/istanbuljs/istanbuljs/commit/bf3a539))


### BREAKING CHANGES

* Node.js 8 is now required





## [5.0.6](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v5.0.5...istanbul-lib-source-maps-v5.0.6) (2024-07-02)


### Bug Fixes

* `istanbul-lib-source-maps` implicit `else` crash edge case ([#789](https://github.com/istanbuljs/istanbuljs/issues/789)) ([bbb5815](https://github.com/istanbuljs/istanbuljs/commit/bbb5815a62f293151447a9e1b4363382a8bf3a2f))

## [5.0.5](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v5.0.4...istanbul-lib-source-maps-v5.0.5) (2024-07-01)


### Bug Fixes

* `istanbul-lib-source-maps` to preserve implicit `else` when sourcemaps are used ([#706](https://github.com/istanbuljs/istanbuljs/issues/706)) ([d16a155](https://github.com/istanbuljs/istanbuljs/commit/d16a155b24bd137803779ad3772b4ea3f265a96f))

## [5.0.4](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v5.0.3...istanbul-lib-source-maps-v5.0.4) (2024-02-26)


### Bug Fixes

* handle missing source map ([1c2017d](https://github.com/istanbuljs/istanbuljs/commit/1c2017d5a3e20ef5725b77a8e4d76eff84b9a62f))

## [5.0.3](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v5.0.2...istanbul-lib-source-maps-v5.0.3) (2024-02-26)


### Bug Fixes

* correct CI check to properly release 5.0.1 ([a39fdfe](https://github.com/istanbuljs/istanbuljs/commit/a39fdfe3a2082ba82ef4243840bcffe10737a40f))

## [5.0.2](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v5.0.1...istanbul-lib-source-maps-v5.0.2) (2024-02-26)


### Bug Fixes

* correct CI badge in source-maps readme ([78aa783](https://github.com/istanbuljs/istanbuljs/commit/78aa783a54760b79d7e6d0f6e0d8c6c481b690d7))

## [5.0.1](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v5.0.0...istanbul-lib-source-maps-v5.0.1) (2024-02-26)


### Bug Fixes

* use `allGeneratedPositionsFor` for more accurate source map transforms ([#771](https://github.com/istanbuljs/istanbuljs/issues/771)) ([dde947c](https://github.com/istanbuljs/istanbuljs/commit/dde947c6ee808b54ebf1ba4faea1f89c43ef3df6))

## [5.0.0](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v4.0.1...istanbul-lib-source-maps-v5.0.0) (2024-02-26)


### ⚠ BREAKING CHANGES

* replace source-map with @jridgewell/trace-mapping ([#685](https://github.com/istanbuljs/istanbuljs/issues/685))

### Bug Fixes

* use `allGeneratedPositionsFor` for more accurate source map transforms ([#768](https://github.com/istanbuljs/istanbuljs/issues/768)) ([c6d0982](https://github.com/istanbuljs/istanbuljs/commit/c6d0982e960f6aed85d9f4c7d1da3b6479bb2272))


### Code Refactoring

* replace source-map with @jridgewell/trace-mapping ([#685](https://github.com/istanbuljs/istanbuljs/issues/685)) ([293f8b9](https://github.com/istanbuljs/istanbuljs/commit/293f8b97767e0a09646ef7a28543a13ffd92074d))

### [4.0.1](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-lib-source-maps-v4.0.0...istanbul-lib-source-maps-v4.0.1) (2021-10-12)


### Bug Fixes

* source mapping for branch statements ([#518](https://www.github.com/istanbuljs/istanbuljs/issues/518)) ([3833708](https://www.github.com/istanbuljs/istanbuljs/commit/38337081d97baa6295707d569dee9c4abc3f7da7))

## [3.0.6](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.6) (2019-04-24)


### Bug Fixes

* if LEAST_UPPER_BOUND returns null, try GREATEST_LOWER_BOUND ([#375](https://github.com/istanbuljs/istanbuljs/issues/375)) ([72b0f05](https://github.com/istanbuljs/istanbuljs/commit/72b0f05))





## [3.0.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.5) (2019-04-09)

**Note:** Version bump only for package istanbul-lib-source-maps





## [3.0.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.4) (2019-04-03)

**Note:** Version bump only for package istanbul-lib-source-maps





## [3.0.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.3) (2019-03-12)


### Bug Fixes

* Map unique files once, regardless of path separator ([#287](https://github.com/istanbuljs/istanbuljs/issues/287)) ([39a1e56](https://github.com/istanbuljs/istanbuljs/commit/39a1e56))





## [3.0.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.2) (2019-01-26)

**Note:** Version bump only for package istanbul-lib-source-maps





<a name="3.0.1"></a>
## [3.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.1) (2018-12-25)


### Bug Fixes

* correct variable name in source-map transform ([#257](https://github.com/istanbuljs/istanbuljs/issues/257)) ([de9c921](https://github.com/istanbuljs/istanbuljs/commit/de9c921))




<a name="3.0.0"></a>
# [3.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@3.0.0) (2018-12-19)


### Bug Fixes

* correctly calculate end position of sourcemap statement  ([f97ffc7](https://github.com/istanbuljs/istanbuljs/commit/f97ffc7))


### BREAKING CHANGES

* coverage output can now contain Infinity, when a range extends past the source in a file.




<a name="2.0.1"></a>
## [2.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@2.0.1) (2018-07-07)




**Note:** Version bump only for package istanbul-lib-source-maps

<a name="2.0.0"></a>
# [2.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@2.0.0) (2018-06-06)


### Bug Fixes

* use null prototype for map objects ([#177](https://github.com/istanbuljs/istanbuljs/issues/177)) ([9a5a30c](https://github.com/istanbuljs/istanbuljs/commit/9a5a30c))


### BREAKING CHANGES

* a null prototype is now used in several places rather than the default `{}` assignment.




<a name="1.2.5"></a>
## [1.2.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@1.2.5) (2018-05-31)


### Bug Fixes

* process.cwd is a function not a string ([#163](https://github.com/istanbuljs/istanbuljs/issues/163)). ([#171](https://github.com/istanbuljs/istanbuljs/issues/171)) ([9c7802c](https://github.com/istanbuljs/istanbuljs/commit/9c7802c))




<a name="1.2.4"></a>
## [1.2.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@1.2.4) (2018-03-04)




**Note:** Version bump only for package istanbul-lib-source-maps

<a name="1.2.3"></a>
## [1.2.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@1.2.3) (2018-02-13)




**Note:** Version bump only for package istanbul-lib-source-maps

<a name="1.2.2"></a>
## [1.2.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@1.2.2) (2017-10-21)




**Note:** Version bump only for package istanbul-lib-source-maps

<a name="1.2.1"></a>
## [1.2.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-source-maps@1.2.1) (2017-05-27)




<a name="1.2.0"></a>
# [1.2.0](https://github.com/istanbuljs/istanbul-lib-source-maps/compare/<EMAIL>-lib-source-maps@1.2.0) (2017-04-29)


### Features

* pull in debug module, to make debug messages optional ([#36](https://github.com/istanbuljs/istanbuljs/issues/36)) ([189519d](https://github.com/istanbuljs/istanbul-lib-source-maps/commit/189519d))




<a name="1.1.1"></a>
## [1.1.1](https://github.com/istanbuljs/istanbul-lib-source-maps/compare/<EMAIL>-lib-source-maps@1.1.1) (2017-03-27)

<a name="1.1.0"></a>
# [1.1.0](https://github.com/istanbuljs/istanbul-lib-source-maps/compare/v1.0.2...v1.1.0) (2016-11-10)


### Features

* read and apply any input source maps stored with coverage data  ([#4](https://github.com/istanbuljs/istanbul-lib-source-maps/issues/4)) ([aea405b](https://github.com/istanbuljs/istanbul-lib-source-maps/commit/aea405b))



<a name="1.0.2"></a>
## [1.0.2](https://github.com/istanbuljs/istanbul-lib-source-maps/compare/v1.0.1...v1.0.2) (2016-10-03)


### Bug Fixes

* broken mapped coverage report ([#6](https://github.com/istanbuljs/istanbul-lib-source-maps/issues/6)) ([d9dd738](https://github.com/istanbuljs/istanbul-lib-source-maps/commit/d9dd738))



<a name="1.0.1"></a>
## [1.0.1](https://github.com/istanbuljs/istanbul-lib-source-maps/compare/v1.0.0...v1.0.1) (2016-09-13)


### Bug Fixes

* position validation shouldn't throw away locations with 0 ([#5](https://github.com/istanbuljs/istanbul-lib-source-maps/issues/5)) ([ac4b72c](https://github.com/istanbuljs/istanbul-lib-source-maps/commit/ac4b72c))



<a name="1.0.0"></a>
# [1.0.0](https://github.com/istanbuljs/istanbul-lib-source-maps/compare/v1.0.0-alpha.9...v1.0.0) (2016-08-31)


### Bug Fixes

* discard more bad source map positions ([#3](https://github.com/istanbuljs/istanbul-lib-source-maps/issues/3)) ([ed7b27f](https://github.com/istanbuljs/istanbul-lib-source-maps/commit/ed7b27f))
