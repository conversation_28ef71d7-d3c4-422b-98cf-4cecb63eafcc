{"name": "@bcoe/v8-coverage", "version": "1.0.2", "description": "Helper functions for V8 coverage files.", "author": "<PERSON> <<EMAIL>> (https://demurgos.net)", "license": "MIT", "main": "./src/lib/index.js", "repository": {"type": "git", "url": "git://github.com/bcoe/v8-coverage.git"}, "scripts": {"test": "c8 mocha ./src/test/*.js", "posttest": "prettier --check ./src/lib/*.js", "fix": "prettier --write ./src/lib/*.js"}, "devDependencies": {"c8": "^10.1.2", "chai": "^4.5.0", "mocha": "^11.0.1", "prettier": "3.4.2"}, "engines": {"node": ">=18"}, "files": ["./src/lib"]}