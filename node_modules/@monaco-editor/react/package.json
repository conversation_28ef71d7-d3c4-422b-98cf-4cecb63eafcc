{"name": "@monaco-editor/react", "version": "4.7.0", "description": "Monaco Editor for React - use the monaco-editor in any React application without needing to use webpack (or rollup/parcel/etc) configuration files / plugins", "author": "<PERSON><PERSON> <<EMAIL>>", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"lint": "eslint src", "build": "tsup", "dev": "tsup --watch", "prepublishOnly": "npm test && npm run lint && npm run build", "test": "vitest run"}, "repository": {"type": "git", "url": "git+https://github.com/suren-atoyan/monaco-react.git"}, "keywords": ["monaco", "editor", "react", "vscode", "code", "text"], "license": "MIT", "bugs": {"url": "https://github.com/suren-atoyan/monaco-react/issues"}, "homepage": "https://github.com/suren-atoyan/monaco-react#readme", "peerDependencies": {"monaco-editor": ">= 0.25.0 < 1", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^8.35.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "husky": "^4.2.3", "prettier": "^2.8.7", "react": "^18.2.0", "react-dom": "^18.2.0", "tsup": "^6.7.0", "typescript": "^4.9.5", "vitest": "^0.29.2"}, "husky": {"hooks": {"pre-commit": "rm -rf ./lib && npm test && npm run lint"}}, "dependencies": {"@monaco-editor/loader": "^1.5.0"}}