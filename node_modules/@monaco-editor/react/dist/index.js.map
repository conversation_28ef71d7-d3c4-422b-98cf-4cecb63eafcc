{"version": 3, "sources": ["../src/index.ts", "../src/DiffEditor/index.ts", "../src/DiffEditor/DiffEditor.tsx", "../src/MonacoContainer/index.ts", "../src/MonacoContainer/MonacoContainer.tsx", "../src/MonacoContainer/styles.ts", "../src/Loading/Loading.tsx", "../src/Loading/styles.ts", "../src/Loading/index.ts", "../src/hooks/useMount/index.ts", "../src/hooks/useUpdate/index.ts", "../src/utils/index.ts", "../src/hooks/useMonaco/index.ts", "../src/Editor/index.ts", "../src/Editor/Editor.tsx", "../src/hooks/usePrevious/index.ts"], "sourcesContent": ["import loader from '@monaco-editor/loader';\nexport { loader };\n\nimport DiffEditor from './DiffEditor';\nexport * from './DiffEditor/types';\nexport { DiffEditor };\n\nimport useMonaco from './hooks/useMonaco';\nexport { useMonaco };\n\nimport Editor from './Editor';\nexport * from './Editor/types';\nexport { Editor };\nexport default Editor;\n\n// Monaco\nimport type * as monaco from 'monaco-editor/esm/vs/editor/editor.api';\nexport type Monaco = typeof monaco;\n\n// Default themes\nexport type Theme = 'vs-dark' | 'light';\n", "import { memo } from 'react';\n\nimport DiffEditor from './DiffEditor';\n\nexport * from './types';\n\nexport default memo(DiffEditor);\n", "'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport MonacoContainer from '../MonacoContainer';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type DiffEditorProps, type MonacoDiffEditor } from './types';\nimport { type Monaco } from '..';\n\nfunction DiffEditor({\n  original,\n  modified,\n  language,\n  originalLanguage,\n  modifiedLanguage,\n  originalModelPath,\n  modifiedModelPath,\n  keepCurrentOriginalModel = false,\n  keepCurrentModifiedModel = false,\n  theme = 'light',\n  loading = 'Loading...',\n  options = {},\n  height = '100%',\n  width = '100%',\n  className,\n  wrapperProps = {},\n  beforeMount = noop,\n  onMount = noop,\n}: DiffEditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const editorRef = useRef<MonacoDiffEditor | null>(null);\n  const monacoRef = useRef<Monaco | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const preventCreation = useRef(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const originalEditor = editorRef.current.getOriginalEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          original || '',\n          originalLanguage || language || 'text',\n          originalModelPath || '',\n        );\n\n        if (model !== originalEditor.getModel()) {\n          originalEditor.setModel(model);\n        }\n      }\n    },\n    [originalModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const modifiedEditor = editorRef.current.getModifiedEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          modified || '',\n          modifiedLanguage || language || 'text',\n          modifiedModelPath || '',\n        );\n\n        if (model !== modifiedEditor.getModel()) {\n          modifiedEditor.setModel(model);\n        }\n      }\n    },\n    [modifiedModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const modifiedEditor = editorRef.current!.getModifiedEditor();\n      if (modifiedEditor.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        modifiedEditor.setValue(modified || '');\n      } else {\n        if (modified !== modifiedEditor.getValue()) {\n          modifiedEditor.executeEdits('', [\n            {\n              range: modifiedEditor.getModel()!.getFullModelRange(),\n              text: modified || '',\n              forceMoveMarkers: true,\n            },\n          ]);\n\n          modifiedEditor.pushUndoStop();\n        }\n      }\n    },\n    [modified],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.getModel()?.original.setValue(original || '');\n    },\n    [original],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const { original, modified } = editorRef.current!.getModel()!;\n\n      monacoRef.current!.editor.setModelLanguage(original, originalLanguage || language || 'text');\n      monacoRef.current!.editor.setModelLanguage(modified, modifiedLanguage || language || 'text');\n    },\n    [language, originalLanguage, modifiedLanguage],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  const setModels = useCallback(() => {\n    if (!monacoRef.current) return;\n    beforeMountRef.current(monacoRef.current);\n    const originalModel = getOrCreateModel(\n      monacoRef.current,\n      original || '',\n      originalLanguage || language || 'text',\n      originalModelPath || '',\n    );\n\n    const modifiedModel = getOrCreateModel(\n      monacoRef.current,\n      modified || '',\n      modifiedLanguage || language || 'text',\n      modifiedModelPath || '',\n    );\n\n    editorRef.current?.setModel({\n      original: originalModel,\n      modified: modifiedModel,\n    });\n  }, [\n    language,\n    modified,\n    modifiedLanguage,\n    original,\n    originalLanguage,\n    originalModelPath,\n    modifiedModelPath,\n  ]);\n\n  const createEditor = useCallback(() => {\n    if (!preventCreation.current && containerRef.current) {\n      editorRef.current = monacoRef.current!.editor.createDiffEditor(containerRef.current, {\n        automaticLayout: true,\n        ...options,\n      });\n\n      setModels();\n\n      monacoRef.current?.editor.setTheme(theme);\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [options, theme, setModels]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  function disposeEditor() {\n    const models = editorRef.current?.getModel();\n\n    if (!keepCurrentOriginalModel) {\n      models?.original?.dispose();\n    }\n\n    if (!keepCurrentModifiedModel) {\n      models?.modified?.dispose();\n    }\n\n    editorRef.current?.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default DiffEditor;\n", "import { memo } from 'react';\n\nimport MonacoContainer from './MonacoContainer';\n\nexport default memo(MonacoContainer);\n", "import React from 'react';\n\nimport styles from './styles';\nimport Loading from '../Loading';\nimport { type ContainerProps } from './types';\n\n// ** forwardref render functions do not support proptypes or defaultprops **\n// one of the reasons why we use a separate prop for passing ref instead of using forwardref\n\nfunction MonacoContainer({\n  width,\n  height,\n  isEditorReady,\n  loading,\n  _ref,\n  className,\n  wrapperProps,\n}: ContainerProps) {\n  return (\n    <section style={{ ...styles.wrapper, width, height }} {...wrapperProps}>\n      {!isEditorReady && <Loading>{loading}</Loading>}\n      <div\n        ref={_ref}\n        style={{ ...styles.fullWidth, ...(!isEditorReady && styles.hide) }}\n        className={className}\n      />\n    </section>\n  );\n}\n\nexport default MonacoContainer;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  wrapper: {\n    display: 'flex',\n    position: 'relative',\n    textAlign: 'initial',\n  },\n  fullWidth: {\n    width: '100%',\n  },\n  hide: {\n    display: 'none',\n  },\n};\n\nexport default styles;\n", "import React, { type PropsWithChildren } from 'react';\n\nimport styles from './styles';\n\nfunction Loading({ children }: PropsWithChildren) {\n  return <div style={styles.container}>{children}</div>;\n}\n\nexport default Loading;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  container: {\n    display: 'flex',\n    height: '100%',\n    width: '100%',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n};\n\nexport default styles;\n", "import Loading from './Loading';\n\nexport default Loading;\n", "import { useEffect, type EffectCallback } from 'react';\n\nfunction useMount(effect: EffectCallback) {\n  useEffect(effect, []);\n}\n\nexport default useMount;\n", "import { useEffect, useRef, type DependencyList, type EffectCallback } from 'react';\n\nfunction useUpdate(effect: EffectCallback, deps: DependencyList, applyChanges = true) {\n  const isInitialMount = useRef(true);\n\n  useEffect(\n    isInitialMount.current || !applyChanges\n      ? () => {\n          isInitialMount.current = false;\n        }\n      : effect,\n    deps,\n  );\n}\n\nexport default useUpdate;\n", "import { type Monaco } from '..';\n\n/**\n * noop is a helper function that does nothing\n * @returns undefined\n */\nfunction noop() {\n  /** no-op */\n}\n\n/**\n * getOrCreateModel is a helper function that will return a model if it exists\n * or create a new model if it does not exist.\n * This is useful for when you want to create a model for a file that may or may not exist yet.\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was found or created\n */\nfunction getOrCreateModel(monaco: Monaco, value: string, language: string, path: string) {\n  return getModel(monaco, path) || createModel(monaco, value, language, path);\n}\n\n/**\n * getModel is a helper function that will return a model if it exists\n * or return undefined if it does not exist.\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model that was found or undefined\n */\nfunction getModel(monaco: Monaco, path: string) {\n  return monaco.editor.getModel(createModelUri(monaco, path));\n}\n\n/**\n * createModel is a helper function that will create a new model\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was created\n */\nfunction createModel(monaco: Monaco, value: string, language?: string, path?: string) {\n  return monaco.editor.createModel(\n    value,\n    language,\n    path ? createModelUri(monaco, path) : undefined,\n  );\n}\n\n/**\n * createModelUri is a helper function that will create a new model uri\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model uri that was created\n */\nfunction createModelUri(monaco: Monaco, path: string) {\n  return monaco.Uri.parse(path);\n}\n\nexport { noop, getOrCreateModel };\n", "import { useState } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport useMount from '../useMount';\n\nfunction useMonaco() {\n  const [monaco, setMonaco] = useState(loader.__getMonacoInstance());\n\n  useMount(() => {\n    let cancelable: ReturnType<typeof loader.init>;\n\n    if (!monaco) {\n      cancelable = loader.init();\n\n      cancelable.then((monaco) => {\n        setMonaco(monaco);\n      });\n    }\n\n    return () => cancelable?.cancel();\n  });\n\n  return monaco;\n}\n\nexport default useMonaco;\n", "import { memo } from 'react';\n\nimport Editor from './Editor';\n\nexport * from './types';\n\nexport default memo(Editor);\n", "'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport loader from '@monaco-editor/loader';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport usePrevious from '../hooks/usePrevious';\nimport { type IDisposable, type editor } from 'monaco-editor';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type EditorProps } from './types';\nimport { type Monaco } from '..';\nimport MonacoContainer from '../MonacoContainer';\n\nconst viewStates = new Map();\n\nfunction Editor({\n  defaultValue,\n  defaultLanguage,\n  defaultPath,\n  value,\n  language,\n  path,\n  /* === */\n  theme = 'light',\n  line,\n  loading = 'Loading...',\n  options = {},\n  overrideServices = {},\n  saveViewState = true,\n  keepCurrentModel = false,\n  /* === */\n  width = '100%',\n  height = '100%',\n  className,\n  wrapperProps = {},\n  /* === */\n  beforeMount = noop,\n  onMount = noop,\n  onChange,\n  onValidate = noop,\n}: EditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const monacoRef = useRef<Monaco | null>(null);\n  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const subscriptionRef = useRef<IDisposable>();\n  const valueRef = useRef(value);\n  const previousPath = usePrevious(path);\n  const preventCreation = useRef(false);\n  const preventTriggerChangeEvent = useRef<boolean>(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      const model = getOrCreateModel(\n        monacoRef.current!,\n        defaultValue || value || '',\n        defaultLanguage || language || '',\n        path || defaultPath || '',\n      );\n\n      if (model !== editorRef.current?.getModel()) {\n        if (saveViewState) viewStates.set(previousPath, editorRef.current?.saveViewState());\n        editorRef.current?.setModel(model);\n        if (saveViewState) editorRef.current?.restoreViewState(viewStates.get(path));\n      }\n    },\n    [path],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (!editorRef.current || value === undefined) return;\n      if (editorRef.current.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        editorRef.current.setValue(value);\n      } else if (value !== editorRef.current.getValue()) {\n        preventTriggerChangeEvent.current = true;\n        editorRef.current.executeEdits('', [\n          {\n            range: editorRef.current.getModel()!.getFullModelRange(),\n            text: value,\n            forceMoveMarkers: true,\n          },\n        ]);\n\n        editorRef.current.pushUndoStop();\n        preventTriggerChangeEvent.current = false;\n      }\n    },\n    [value],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const model = editorRef.current?.getModel();\n      if (model && language) monacoRef.current?.editor.setModelLanguage(model, language);\n    },\n    [language],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      // reason for undefined check: https://github.com/suren-atoyan/monaco-react/pull/188\n      if (line !== undefined) {\n        editorRef.current?.revealLine(line);\n      }\n    },\n    [line],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  const createEditor = useCallback(() => {\n    if (!containerRef.current || !monacoRef.current) return;\n    if (!preventCreation.current) {\n      beforeMountRef.current(monacoRef.current);\n      const autoCreatedModelPath = path || defaultPath;\n\n      const defaultModel = getOrCreateModel(\n        monacoRef.current,\n        value || defaultValue || '',\n        defaultLanguage || language || '',\n        autoCreatedModelPath || '',\n      );\n\n      editorRef.current = monacoRef.current?.editor.create(\n        containerRef.current,\n        {\n          model: defaultModel,\n          automaticLayout: true,\n          ...options,\n        },\n        overrideServices,\n      );\n\n      saveViewState && editorRef.current.restoreViewState(viewStates.get(autoCreatedModelPath));\n\n      monacoRef.current.editor.setTheme(theme);\n\n      if (line !== undefined) {\n        editorRef.current.revealLine(line);\n      }\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [\n    defaultValue,\n    defaultLanguage,\n    defaultPath,\n    value,\n    language,\n    path,\n    options,\n    overrideServices,\n    saveViewState,\n    theme,\n    line,\n  ]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  // subscription\n  // to avoid unnecessary updates (attach - dispose listener) in subscription\n  valueRef.current = value;\n\n  // onChange\n  useEffect(() => {\n    if (isEditorReady && onChange) {\n      subscriptionRef.current?.dispose();\n      subscriptionRef.current = editorRef.current?.onDidChangeModelContent((event) => {\n        if (!preventTriggerChangeEvent.current) {\n          onChange(editorRef.current!.getValue(), event);\n        }\n      });\n    }\n  }, [isEditorReady, onChange]);\n\n  // onValidate\n  useEffect(() => {\n    if (isEditorReady) {\n      const changeMarkersListener = monacoRef.current!.editor.onDidChangeMarkers((uris) => {\n        const editorUri = editorRef.current!.getModel()?.uri;\n\n        if (editorUri) {\n          const currentEditorHasMarkerChanges = uris.find((uri) => uri.path === editorUri.path);\n          if (currentEditorHasMarkerChanges) {\n            const markers = monacoRef.current!.editor.getModelMarkers({\n              resource: editorUri,\n            });\n            onValidate?.(markers);\n          }\n        }\n      });\n\n      return () => {\n        changeMarkersListener?.dispose();\n      };\n    }\n    return () => {\n      // eslint happy\n    };\n  }, [isEditorReady, onValidate]);\n\n  function disposeEditor() {\n    subscriptionRef.current?.dispose();\n\n    if (keepCurrentModel) {\n      saveViewState && viewStates.set(path, editorRef.current!.saveViewState());\n    } else {\n      editorRef.current!.getModel()?.dispose();\n    }\n\n    editorRef.current!.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default Editor;\n", "import { useEffect, useRef } from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n\nexport default usePrevious;\n"], "mappings": "ykBAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,gBAAAE,GAAA,WAAAC,EAAA,YAAAC,GAAA,cAAAC,QAAA,cAAAC,KAAA,eAAAC,GAAAP,IAAA,IAAAQ,GAAmB,oCCAnB,IAAAC,GAAqB,iBCErB,IAAAC,EAAgE,oBAChEC,GAAmB,oCCHnB,IAAAC,GAAqB,iBCArB,IAAAC,EAAkB,oBCElB,IAAMC,GAAwC,CAC5C,QAAS,CACP,QAAS,OACT,SAAU,WACV,UAAW,SACb,EACA,UAAW,CACT,MAAO,MACT,EACA,KAAM,CACJ,QAAS,MACX,CACF,EAEOC,EAAQD,GChBf,IAAAE,GAA8C,oBCE9C,IAAMC,GAAwC,CAC5C,UAAW,CACT,QAAS,OACT,OAAQ,OACR,MAAO,OACP,eAAgB,SAChB,WAAY,QACd,CACF,EAEOC,GAAQD,GDRf,SAASE,GAAQ,CAAE,SAAAC,CAAS,EAAsB,CAChD,OAAO,GAAAC,QAAA,cAAC,OAAI,MAAOC,GAAO,WAAYF,CAAS,CACjD,CAEA,IAAOG,GAAQJ,GENf,IAAOK,GAAQA,GJOf,SAASC,GAAgB,CACvB,MAAAC,EACA,OAAAC,EACA,cAAAC,EACA,QAAAC,EACA,KAAAC,EACA,UAAAC,EACA,aAAAC,CACF,EAAmB,CACjB,OACE,EAAAC,QAAA,cAAC,WAAQ,MAAO,CAAE,GAAGC,EAAO,QAAS,MAAAR,EAAO,OAAAC,CAAO,EAAI,GAAGK,GACvD,CAACJ,GAAiB,EAAAK,QAAA,cAACE,GAAA,KAASN,CAAQ,EACrC,EAAAI,QAAA,cAAC,OACC,IAAKH,EACL,MAAO,CAAE,GAAGI,EAAO,UAAW,GAAI,CAACN,GAAiBM,EAAO,IAAM,EACjE,UAAWH,EACb,CACF,CAEJ,CAEA,IAAOK,GAAQX,GD1Bf,IAAOY,KAAQ,SAAKA,EAAe,EMJnC,IAAAC,GAA+C,iBAE/C,SAASC,GAASC,EAAwB,IACxC,cAAUA,EAAQ,CAAC,CAAC,CACtB,CAEA,IAAOC,EAAQF,GCNf,IAAAG,EAA4E,iBAE5E,SAASC,GAAUC,EAAwBC,EAAsBC,EAAe,GAAM,CACpF,IAAMC,KAAiB,UAAO,EAAI,KAElC,aACEA,EAAe,SAAW,CAACD,EACvB,IAAM,CACJC,EAAe,QAAU,EAC3B,EACAH,EACJC,CACF,CACF,CAEA,IAAOG,EAAQL,GCTf,SAASM,GAAO,CAEhB,CAYA,SAASC,EAAiBC,EAAgBC,EAAeC,EAAkBC,EAAc,CACvF,OAAOC,GAASJ,EAAQG,CAAI,GAAKE,GAAYL,EAAQC,EAAOC,EAAUC,CAAI,CAC5E,CASA,SAASC,GAASJ,EAAgBG,EAAc,CAC9C,OAAOH,EAAO,OAAO,SAASM,GAAeN,EAAQG,CAAI,CAAC,CAC5D,CAUA,SAASE,GAAYL,EAAgBC,EAAeC,EAAmBC,EAAe,CACpF,OAAOH,EAAO,OAAO,YACnBC,EACAC,EACAC,EAAOG,GAAeN,EAAQG,CAAI,EAAI,MACxC,CACF,CAQA,SAASG,GAAeN,EAAgBG,EAAc,CACpD,OAAOH,EAAO,IAAI,MAAMG,CAAI,CAC9B,CT/CA,SAASI,GAAW,CAClB,SAAAC,EACA,SAAAC,EACA,SAAAC,EACA,iBAAAC,EACA,iBAAAC,EACA,kBAAAC,EACA,kBAAAC,EACA,yBAAAC,EAA2B,GAC3B,yBAAAC,EAA2B,GAC3B,MAAAC,EAAQ,QACR,QAAAC,EAAU,aACV,QAAAC,EAAU,CAAC,EACX,OAAAC,EAAS,OACT,MAAAC,EAAQ,OACR,UAAAC,EACA,aAAAC,EAAe,CAAC,EAChB,YAAAC,EAAcC,EACd,QAAAC,EAAUD,CACZ,EAAoB,CAClB,GAAM,CAACE,EAAeC,CAAgB,KAAI,YAAS,EAAK,EAClD,CAACC,EAAkBC,CAAmB,KAAI,YAAS,EAAI,EACvDC,KAAY,UAAgC,IAAI,EAChDC,KAAY,UAAsB,IAAI,EACtCC,KAAe,UAAuB,IAAI,EAC1CC,KAAa,UAAOR,CAAO,EAC3BS,KAAiB,UAAOX,CAAW,EACnCY,KAAkB,UAAO,EAAK,EAEpCC,EAAS,IAAM,CACb,IAAMC,EAAa,GAAAC,QAAO,KAAK,EAE/B,OAAAD,EACG,KAAME,IAAYR,EAAU,QAAUQ,IAAWV,EAAoB,EAAK,CAAC,EAC3E,MACEW,GACCA,GAAO,OAAS,eAAiB,QAAQ,MAAM,gCAAiCA,CAAK,CACzF,EAEK,IAAOV,EAAU,QAAUW,EAAc,EAAIJ,EAAW,OAAO,CACxE,CAAC,EAEDK,EACE,IAAM,CACJ,GAAIZ,EAAU,SAAWC,EAAU,QAAS,CAC1C,IAAMY,EAAiBb,EAAU,QAAQ,kBAAkB,EACrDc,EAAQC,EACZd,EAAU,QACVxB,GAAY,GACZG,GAAoBD,GAAY,OAChCG,GAAqB,EACvB,EAEIgC,IAAUD,EAAe,SAAS,GACpCA,EAAe,SAASC,CAAK,EAGnC,EACA,CAAChC,CAAiB,EAClBc,CACF,EAEAgB,EACE,IAAM,CACJ,GAAIZ,EAAU,SAAWC,EAAU,QAAS,CAC1C,IAAMe,EAAiBhB,EAAU,QAAQ,kBAAkB,EACrDc,EAAQC,EACZd,EAAU,QACVvB,GAAY,GACZG,GAAoBF,GAAY,OAChCI,GAAqB,EACvB,EAEI+B,IAAUE,EAAe,SAAS,GACpCA,EAAe,SAASF,CAAK,EAGnC,EACA,CAAC/B,CAAiB,EAClBa,CACF,EAEAgB,EACE,IAAM,CACJ,IAAMI,EAAiBhB,EAAU,QAAS,kBAAkB,EACxDgB,EAAe,UAAUf,EAAU,QAAS,OAAO,aAAa,QAAQ,EAC1Ee,EAAe,SAAStC,GAAY,EAAE,EAElCA,IAAasC,EAAe,SAAS,IACvCA,EAAe,aAAa,GAAI,CAC9B,CACE,MAAOA,EAAe,SAAS,EAAG,kBAAkB,EACpD,KAAMtC,GAAY,GAClB,iBAAkB,EACpB,CACF,CAAC,EAEDsC,EAAe,aAAa,EAGlC,EACA,CAACtC,CAAQ,EACTkB,CACF,EAEAgB,EACE,IAAM,CACJZ,EAAU,SAAS,SAAS,GAAG,SAAS,SAASvB,GAAY,EAAE,CACjE,EACA,CAACA,CAAQ,EACTmB,CACF,EAEAgB,EACE,IAAM,CACJ,GAAM,CAAE,SAAAnC,EAAU,SAAAC,CAAS,EAAIsB,EAAU,QAAS,SAAS,EAE3DC,EAAU,QAAS,OAAO,iBAAiBxB,EAAUG,GAAoBD,GAAY,MAAM,EAC3FsB,EAAU,QAAS,OAAO,iBAAiBvB,EAAUG,GAAoBF,GAAY,MAAM,CAC7F,EACA,CAACA,EAAUC,EAAkBC,CAAgB,EAC7Ce,CACF,EAEAgB,EACE,IAAM,CACJX,EAAU,SAAS,OAAO,SAASf,CAAK,CAC1C,EACA,CAACA,CAAK,EACNU,CACF,EAEAgB,EACE,IAAM,CACJZ,EAAU,SAAS,cAAcZ,CAAO,CAC1C,EACA,CAACA,CAAO,EACRQ,CACF,EAEA,IAAMqB,KAAY,eAAY,IAAM,CAClC,GAAI,CAAChB,EAAU,QAAS,OACxBG,EAAe,QAAQH,EAAU,OAAO,EACxC,IAAMiB,EAAgBH,EACpBd,EAAU,QACVxB,GAAY,GACZG,GAAoBD,GAAY,OAChCG,GAAqB,EACvB,EAEMqC,EAAgBJ,EACpBd,EAAU,QACVvB,GAAY,GACZG,GAAoBF,GAAY,OAChCI,GAAqB,EACvB,EAEAiB,EAAU,SAAS,SAAS,CAC1B,SAAUkB,EACV,SAAUC,CACZ,CAAC,CACH,EAAG,CACDxC,EACAD,EACAG,EACAJ,EACAG,EACAE,EACAC,CACF,CAAC,EAEKqC,KAAe,eAAY,IAAM,CACjC,CAACf,EAAgB,SAAWH,EAAa,UAC3CF,EAAU,QAAUC,EAAU,QAAS,OAAO,iBAAiBC,EAAa,QAAS,CACnF,gBAAiB,GACjB,GAAGd,CACL,CAAC,EAED6B,EAAU,EAEVhB,EAAU,SAAS,OAAO,SAASf,CAAK,EAExCW,EAAiB,EAAI,EACrBQ,EAAgB,QAAU,GAE9B,EAAG,CAACjB,EAASF,EAAO+B,CAAS,CAAC,KAE9B,aAAU,IAAM,CACVrB,GACFO,EAAW,QAAQH,EAAU,QAAUC,EAAU,OAAQ,CAE7D,EAAG,CAACL,CAAa,CAAC,KAElB,aAAU,IAAM,CACd,CAACE,GAAoB,CAACF,GAAiBwB,EAAa,CACtD,EAAG,CAACtB,EAAkBF,EAAewB,CAAY,CAAC,EAElD,SAAST,GAAgB,CACvB,IAAMU,EAASrB,EAAU,SAAS,SAAS,EAEtChB,GACHqC,GAAQ,UAAU,QAAQ,EAGvBpC,GACHoC,GAAQ,UAAU,QAAQ,EAG5BrB,EAAU,SAAS,QAAQ,CAC7B,CAEA,OACE,EAAAsB,QAAA,cAACC,EAAA,CACC,MAAOjC,EACP,OAAQD,EACR,cAAeO,EACf,QAAST,EACT,KAAMe,EACN,UAAWX,EACX,aAAcC,EAChB,CAEJ,CAEA,IAAOgC,GAAQhD,GDtOf,IAAOiD,MAAQ,SAAKA,EAAU,EWN9B,IAAAC,GAAyB,iBACzBC,EAAmB,oCAInB,SAASC,IAAY,CACnB,GAAM,CAACC,EAAQC,CAAS,KAAI,aAAS,EAAAC,QAAO,oBAAoB,CAAC,EAEjE,OAAAC,EAAS,IAAM,CACb,IAAIC,EAEJ,OAAKJ,IACHI,EAAa,EAAAF,QAAO,KAAK,EAEzBE,EAAW,KAAMJ,GAAW,CAC1BC,EAAUD,CAAM,CAClB,CAAC,GAGI,IAAMI,GAAY,OAAO,CAClC,CAAC,EAEMJ,CACT,CAEA,IAAOK,GAAQN,GCzBf,IAAAO,GAAqB,iBCErB,IAAAC,EAAgE,oBAChEC,GAAmB,oCCHnB,IAAAC,EAAkC,iBAElC,SAASC,GAAeC,EAAU,CAChC,IAAMC,KAAM,UAAU,EAEtB,sBAAU,IAAM,CACdA,EAAI,QAAUD,CAChB,EAAG,CAACA,CAAK,CAAC,EAEHC,EAAI,OACb,CAEA,IAAOC,GAAQH,GDCf,IAAMI,EAAa,IAAI,IAEvB,SAASC,GAAO,CACd,aAAAC,EACA,gBAAAC,EACA,YAAAC,EACA,MAAAC,EACA,SAAAC,EACA,KAAAC,EAEA,MAAAC,EAAQ,QACR,KAAAC,EACA,QAAAC,EAAU,aACV,QAAAC,EAAU,CAAC,EACX,iBAAAC,EAAmB,CAAC,EACpB,cAAAC,EAAgB,GAChB,iBAAAC,EAAmB,GAEnB,MAAAC,EAAQ,OACR,OAAAC,EAAS,OACT,UAAAC,EACA,aAAAC,EAAe,CAAC,EAEhB,YAAAC,EAAcC,EACd,QAAAC,EAAUD,EACV,SAAAE,EACA,WAAAC,EAAaH,CACf,EAAgB,CACd,GAAM,CAACI,EAAeC,CAAgB,KAAI,YAAS,EAAK,EAClD,CAACC,EAAkBC,CAAmB,KAAI,YAAS,EAAI,EACvDC,KAAY,UAAsB,IAAI,EACtCC,KAAY,UAA4C,IAAI,EAC5DC,KAAe,UAAuB,IAAI,EAC1CC,KAAa,UAAOV,CAAO,EAC3BW,KAAiB,UAAOb,CAAW,EACnCc,KAAkB,UAAoB,EACtCC,KAAW,UAAO7B,CAAK,EACvB8B,EAAeC,GAAY7B,CAAI,EAC/B8B,MAAkB,UAAO,EAAK,EAC9BC,KAA4B,UAAgB,EAAK,EAEvDC,EAAS,IAAM,CACb,IAAMC,EAAa,GAAAC,QAAO,KAAK,EAE/B,OAAAD,EACG,KAAME,IAAYd,EAAU,QAAUc,IAAWf,EAAoB,EAAK,CAAC,EAC3E,MACEgB,GACCA,GAAO,OAAS,eAAiB,QAAQ,MAAM,gCAAiCA,CAAK,CACzF,EAEK,IAAOd,EAAU,QAAUe,GAAc,EAAIJ,EAAW,OAAO,CACxE,CAAC,EAEDK,EACE,IAAM,CACJ,IAAMC,EAAQC,EACZnB,EAAU,QACV1B,GAAgBG,GAAS,GACzBF,GAAmBG,GAAY,GAC/BC,GAAQH,GAAe,EACzB,EAEI0C,IAAUjB,EAAU,SAAS,SAAS,IACpChB,GAAeb,EAAW,IAAImC,EAAcN,EAAU,SAAS,cAAc,CAAC,EAClFA,EAAU,SAAS,SAASiB,CAAK,EAC7BjC,GAAegB,EAAU,SAAS,iBAAiB7B,EAAW,IAAIO,CAAI,CAAC,EAE/E,EACA,CAACA,CAAI,EACLiB,CACF,EAEAqB,EACE,IAAM,CACJhB,EAAU,SAAS,cAAclB,CAAO,CAC1C,EACA,CAACA,CAAO,EACRa,CACF,EAEAqB,EACE,IAAM,CACA,CAAChB,EAAU,SAAWxB,IAAU,SAChCwB,EAAU,QAAQ,UAAUD,EAAU,QAAS,OAAO,aAAa,QAAQ,EAC7EC,EAAU,QAAQ,SAASxB,CAAK,EACvBA,IAAUwB,EAAU,QAAQ,SAAS,IAC9CS,EAA0B,QAAU,GACpCT,EAAU,QAAQ,aAAa,GAAI,CACjC,CACE,MAAOA,EAAU,QAAQ,SAAS,EAAG,kBAAkB,EACvD,KAAMxB,EACN,iBAAkB,EACpB,CACF,CAAC,EAEDwB,EAAU,QAAQ,aAAa,EAC/BS,EAA0B,QAAU,IAExC,EACA,CAACjC,CAAK,EACNmB,CACF,EAEAqB,EACE,IAAM,CACJ,IAAMC,EAAQjB,EAAU,SAAS,SAAS,EACtCiB,GAASxC,GAAUsB,EAAU,SAAS,OAAO,iBAAiBkB,EAAOxC,CAAQ,CACnF,EACA,CAACA,CAAQ,EACTkB,CACF,EAEAqB,EACE,IAAM,CAEApC,IAAS,QACXoB,EAAU,SAAS,WAAWpB,CAAI,CAEtC,EACA,CAACA,CAAI,EACLe,CACF,EAEAqB,EACE,IAAM,CACJjB,EAAU,SAAS,OAAO,SAASpB,CAAK,CAC1C,EACA,CAACA,CAAK,EACNgB,CACF,EAEA,IAAMwB,MAAe,eAAY,IAAM,CACrC,GAAI,GAAClB,EAAa,SAAW,CAACF,EAAU,UACpC,CAACS,GAAgB,QAAS,CAC5BL,EAAe,QAAQJ,EAAU,OAAO,EACxC,IAAMqB,EAAuB1C,GAAQH,EAE/B8C,EAAeH,EACnBnB,EAAU,QACVvB,GAASH,GAAgB,GACzBC,GAAmBG,GAAY,GAC/B2C,GAAwB,EAC1B,EAEApB,EAAU,QAAUD,EAAU,SAAS,OAAO,OAC5CE,EAAa,QACb,CACE,MAAOoB,EACP,gBAAiB,GACjB,GAAGvC,CACL,EACAC,CACF,EAEAC,GAAiBgB,EAAU,QAAQ,iBAAiB7B,EAAW,IAAIiD,CAAoB,CAAC,EAExFrB,EAAU,QAAQ,OAAO,SAASpB,CAAK,EAEnCC,IAAS,QACXoB,EAAU,QAAQ,WAAWpB,CAAI,EAGnCgB,EAAiB,EAAI,EACrBY,GAAgB,QAAU,GAE9B,EAAG,CACDnC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAI,EACAC,EACAC,EACAL,EACAC,CACF,CAAC,KAED,aAAU,IAAM,CACVe,GACFO,EAAW,QAAQF,EAAU,QAAUD,EAAU,OAAQ,CAE7D,EAAG,CAACJ,CAAa,CAAC,KAElB,aAAU,IAAM,CACd,CAACE,GAAoB,CAACF,GAAiBwB,GAAa,CACtD,EAAG,CAACtB,EAAkBF,EAAewB,EAAY,CAAC,EAIlDd,EAAS,QAAU7B,KAGnB,aAAU,IAAM,CACVmB,GAAiBF,IACnBW,EAAgB,SAAS,QAAQ,EACjCA,EAAgB,QAAUJ,EAAU,SAAS,wBAAyBsB,GAAU,CACzEb,EAA0B,SAC7BhB,EAASO,EAAU,QAAS,SAAS,EAAGsB,CAAK,CAEjD,CAAC,EAEL,EAAG,CAAC3B,EAAeF,CAAQ,CAAC,KAG5B,aAAU,IAAM,CACd,GAAIE,EAAe,CACjB,IAAM4B,EAAwBxB,EAAU,QAAS,OAAO,mBAAoByB,GAAS,CACnF,IAAMC,EAAYzB,EAAU,QAAS,SAAS,GAAG,IAEjD,GAAIyB,GACoCD,EAAK,KAAME,GAAQA,EAAI,OAASD,EAAU,IAAI,EACjD,CACjC,IAAME,EAAU5B,EAAU,QAAS,OAAO,gBAAgB,CACxD,SAAU0B,CACZ,CAAC,EACD/B,IAAaiC,CAAO,EAG1B,CAAC,EAED,MAAO,IAAM,CACXJ,GAAuB,QAAQ,CACjC,EAEF,MAAO,IAAM,CAEb,CACF,EAAG,CAAC5B,EAAeD,CAAU,CAAC,EAE9B,SAASqB,IAAgB,CACvBX,EAAgB,SAAS,QAAQ,EAE7BnB,EACFD,GAAiBb,EAAW,IAAIO,EAAMsB,EAAU,QAAS,cAAc,CAAC,EAExEA,EAAU,QAAS,SAAS,GAAG,QAAQ,EAGzCA,EAAU,QAAS,QAAQ,CAC7B,CAEA,OACE,EAAA4B,QAAA,cAACC,EAAA,CACC,MAAO3C,EACP,OAAQC,EACR,cAAeQ,EACf,QAASd,EACT,KAAMoB,EACN,UAAWb,EACX,aAAcC,EAChB,CAEJ,CAEA,IAAOyC,GAAQ1D,GDxQf,IAAO2D,KAAQ,SAAKA,EAAM,EbO1B,IAAOC,GAAQC", "names": ["src_exports", "__export", "DiffEditor_default", "Editor_default", "src_default", "loader", "useMonaco_default", "__toCommonJS", "import_loader", "import_react", "import_react", "import_loader", "import_react", "import_react", "styles", "styles_default", "import_react", "styles", "styles_default", "Loading", "children", "React", "styles_default", "Loading_default", "Loading_default", "MonacoContainer", "width", "height", "isEditorReady", "loading", "_ref", "className", "wrapperProps", "React", "styles_default", "Loading_default", "MonacoContainer_default", "MonacoContainer_default", "import_react", "useMount", "effect", "useMount_default", "import_react", "useUpdate", "effect", "deps", "applyChanges", "isInitialMount", "useUpdate_default", "noop", "getOrCreateModel", "monaco", "value", "language", "path", "getModel", "createModel", "createModelUri", "DiffE<PERSON>or", "original", "modified", "language", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "keepCurrentModifiedModel", "theme", "loading", "options", "height", "width", "className", "wrapperProps", "beforeMount", "noop", "onMount", "isEditorReady", "setIsEditorReady", "isMonacoMounting", "setIsMonacoMounting", "editor<PERSON><PERSON>", "monacoRef", "containerRef", "onMountRef", "beforeMountRef", "preventCreation", "useMount_default", "cancelable", "loader", "monaco", "error", "dispose<PERSON><PERSON><PERSON>", "useUpdate_default", "originalEditor", "model", "getOrCreateModel", "modifiedEditor", "setModels", "originalModel", "modifiedModel", "createEditor", "models", "React", "MonacoContainer_default", "DiffEditor_default", "DiffEditor_default", "import_react", "import_loader", "useMonaco", "monaco", "setMonaco", "loader", "useMount_default", "cancelable", "useMonaco_default", "import_react", "import_react", "import_loader", "import_react", "usePrevious", "value", "ref", "usePrevious_default", "viewStates", "Editor", "defaultValue", "defaultLanguage", "defaultPath", "value", "language", "path", "theme", "line", "loading", "options", "overrideServices", "saveViewState", "keepCurrentModel", "width", "height", "className", "wrapperProps", "beforeMount", "noop", "onMount", "onChange", "onValidate", "isEditorReady", "setIsEditorReady", "isMonacoMounting", "setIsMonacoMounting", "monacoRef", "editor<PERSON><PERSON>", "containerRef", "onMountRef", "beforeMountRef", "subscriptionRef", "valueRef", "previousPath", "usePrevious_default", "preventCreation", "preventTriggerChangeEvent", "useMount_default", "cancelable", "loader", "monaco", "error", "dispose<PERSON><PERSON><PERSON>", "useUpdate_default", "model", "getOrCreateModel", "createEditor", "autoCreatedModelPath", "defaultModel", "event", "changeMarkersListener", "uris", "editor<PERSON><PERSON>", "uri", "markers", "React", "MonacoContainer_default", "Editor_default", "Editor_default", "src_default", "Editor_default"]}