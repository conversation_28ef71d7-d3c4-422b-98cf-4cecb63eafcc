import { describe, it, expect, beforeEach, vi } from 'vitest'
import { create } from 'zustand'
import { Novel, Chapter } from '@/shared/types'

// Mock the electron API
const mockElectron = {
  databaseQuery: vi.fn(),
  databaseRun: vi.fn()
}

// @ts-ignore
global.window = {
  electron: mockElectron
}

// Import the store after mocking
const { useNovelStore } = require('@/stores/novel')

describe('Novel Store', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset store state
    const { getState } = useNovelStore
    getState().clearError()
    getState().setLoading(false)
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useNovelStore.getState()
      
      expect(state.novels).toEqual([])
      expect(state.currentNovel).toBeNull()
      expect(state.chapters).toEqual([])
      expect(state.characters).toEqual([])
      expect(state.outlines).toEqual([])
      expect(state.materials).toEqual([])
      expect(state.loading).toBe(false)
      expect(state.error).toBeNull()
    })
  })

  describe('loadNovels', () => {
    it('should load novels successfully', async () => {
      const mockNovels = [
        {
          id: 'novel_1',
          title: 'Test Novel',
          author: 'Test Author',
          description: 'Test Description',
          genre: '["fantasy"]',
          tags: '["test"]',
          created_at: '2024-01-01T00:00:00.000Z',
          updated_at: '2024-01-01T00:00:00.000Z'
        }
      ]
      
      mockElectron.databaseQuery.mockResolvedValue(mockNovels)
      
      const { loadNovels } = useNovelStore.getState()
      await loadNovels()
      
      const state = useNovelStore.getState()
      expect(state.novels).toHaveLength(1)
      expect(state.novels[0].title).toBe('Test Novel')
      expect(state.novels[0].genre).toEqual(['fantasy'])
      expect(state.novels[0].tags).toEqual(['test'])
      expect(state.loading).toBe(false)
      expect(state.error).toBeNull()
    })

    it('should handle error when loading novels', async () => {
      const errorMessage = 'Database error'
      mockElectron.databaseQuery.mockRejectedValue(new Error(errorMessage))
      
      const { loadNovels } = useNovelStore.getState()
      await loadNovels()
      
      const state = useNovelStore.getState()
      expect(state.novels).toEqual([])
      expect(state.loading).toBe(false)
      expect(state.error).toBe(errorMessage)
    })
  })

  describe('createNovel', () => {
    it('should create novel successfully', async () => {
      const novelData = {
        title: 'New Novel',
        author: 'New Author',
        description: 'New Description',
        genre: ['romance'],
        tags: ['new'],
        wordCount: 0,
        chapterCount: 0,
        status: 'draft' as const,
        coverImage: ''
      }
      
      mockElectron.databaseRun.mockResolvedValue(undefined)
      
      const { createNovel } = useNovelStore.getState()
      const result = await createNovel(novelData)
      
      expect(result).toMatch(/^novel_/)
      
      const state = useNovelStore.getState()
      expect(state.novels).toHaveLength(1)
      expect(state.novels[0].title).toBe('New Novel')
      expect(state.loading).toBe(false)
      expect(state.error).toBeNull()
    })

    it('should handle error when creating novel', async () => {
      const errorMessage = 'Create failed'
      mockElectron.databaseRun.mockRejectedValue(new Error(errorMessage))
      
      const { createNovel } = useNovelStore.getState()
      
      await expect(createNovel({
        title: 'Test',
        author: 'Test',
        description: '',
        genre: [],
        tags: [],
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        coverImage: ''
      })).rejects.toThrow(errorMessage)
      
      const state = useNovelStore.getState()
      expect(state.error).toBe(errorMessage)
    })
  })

  describe('setCurrentNovel', () => {
    it('should set current novel', () => {
      const novel: Novel = {
        id: 'novel_1',
        title: 'Test Novel',
        author: 'Test Author',
        description: 'Test Description',
        genre: ['fantasy'],
        tags: ['test'],
        wordCount: 1000,
        chapterCount: 5,
        status: 'draft',
        coverImage: '',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      const { setCurrentNovel } = useNovelStore.getState()
      setCurrentNovel(novel)
      
      const state = useNovelStore.getState()
      expect(state.currentNovel).toBe(novel)
    })

    it('should clear current novel when setting null', () => {
      const { setCurrentNovel } = useNovelStore.getState()
      setCurrentNovel(null)
      
      const state = useNovelStore.getState()
      expect(state.currentNovel).toBeNull()
    })
  })

  describe('clearError', () => {
    it('should clear error state', () => {
      const { setError, clearError } = useNovelStore.getState()
      
      // Set error first
      setError('Test error')
      
      const stateBefore = useNovelStore.getState()
      expect(stateBefore.error).toBe('Test error')
      
      // Clear error
      clearError()
      
      const stateAfter = useNovelStore.getState()
      expect(stateAfter.error).toBeNull()
    })
  })

  describe('setLoading', () => {
    it('should set loading state', () => {
      const { setLoading } = useNovelStore.getState()
      
      setLoading(true)
      expect(useNovelStore.getState().loading).toBe(true)
      
      setLoading(false)
      expect(useNovelStore.getState().loading).toBe(false)
    })
  })
})