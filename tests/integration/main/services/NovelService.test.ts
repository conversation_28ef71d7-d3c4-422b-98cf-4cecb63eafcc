import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { NovelService } from '@main/services/NovelService'
import { DatabaseService } from '@main/database/DatabaseService'
import { Novel, Chapter } from '@shared/types'

// Mock DatabaseService for integration testing
class MockDatabaseService {
  private data: Map<string, any[]> = new Map()
  private idCounter = 1

  async insert(table: string, data: any): Promise<void> {
    if (!this.data.has(table)) {
      this.data.set(table, [])
    }
    this.data.get(table)!.push({ ...data, _id: this.idCounter++ })
  }

  async findById(table: string, id: string): Promise<any> {
    const rows = this.data.get(table) || []
    return rows.find(row => row.id === id) || null
  }

  async findAll(table: string, condition?: string, params?: any[]): Promise<any[]> {
    const rows = this.data.get(table) || []
    
    if (!condition || condition === '1=1 ORDER BY updated_at DESC') {
      return rows
    }
    
    // Simple condition parsing for tests
    if (condition.includes('novel_id = ?')) {
      return rows.filter(row => row.novel_id === params![0])
    }
    
    if (condition.includes('ORDER BY order_index')) {
      return rows.sort((a, b) => a.order_index - b.order_index)
    }
    
    return rows
  }

  async update(table: string, id: string, data: any): Promise<void> {
    const rows = this.data.get(table) || []
    const index = rows.findIndex(row => row.id === id)
    if (index !== -1) {
      rows[index] = { ...rows[index], ...data }
    }
  }

  async delete(table: string, id: string): Promise<void> {
    const rows = this.data.get(table) || []
    const index = rows.findIndex(row => row.id === id)
    if (index !== -1) {
      rows.splice(index, 1)
    }
  }

  async get(sql: string, params?: any[]): Promise<any> {
    // Simple SQL parsing for tests
    if (sql.includes('SELECT novel_id FROM chapters WHERE id = ?')) {
      const chapters = this.data.get('chapters') || []
      const chapter = chapters.find(c => c.id === params![0])
      return chapter ? { novel_id: chapter.novel_id } : null
    }
    
    if (sql.includes('SELECT SUM(word_count) as total FROM chapters WHERE novel_id = ?')) {
      const chapters = this.data.get('chapters') || []
      const novelChapters = chapters.filter(c => c.novel_id === params![0])
      const total = novelChapters.reduce((sum, c) => sum + (c.word_count || 0), 0)
      return { total }
    }
    
    return null
  }

  async count(table: string, condition?: string, params?: any[]): Promise<number> {
    const rows = this.data.get(table) || []
    
    if (!condition) {
      return rows.length
    }
    
    if (condition.includes('novel_id = ?')) {
      return rows.filter(row => row.novel_id === params![0]).length
    }
    
    return rows.length
  }

  // Helper method to clear all data
  clear() {
    this.data.clear()
    this.idCounter = 1
  }
}

describe('NovelService Integration Tests', () => {
  let novelService: NovelService
  let mockDb: MockDatabaseService

  beforeEach(() => {
    mockDb = new MockDatabaseService() as any
    novelService = new NovelService(mockDb as any)
  })

  afterEach(() => {
    mockDb.clear()
  })

  describe('Novel Operations', () => {
    it('should create a novel successfully', async () => {
      const novelData = {
        title: 'Test Novel',
        author: 'Test Author',
        description: 'Test Description',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft' as const,
        genre: ['fantasy'],
        tags: ['test']
      }

      const novel = await novelService.createNovel(novelData)

      expect(novel.id).toBeDefined()
      expect(novel.title).toBe('Test Novel')
      expect(novel.author).toBe('Test Author')
      expect(novel.createdAt).toBeInstanceOf(Date)
      expect(novel.updatedAt).toBeInstanceOf(Date)
    })

    it('should retrieve a novel by id', async () => {
      const novelData = {
        title: 'Test Novel',
        author: 'Test Author',
        description: 'Test Description',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft' as const,
        genre: ['fantasy'],
        tags: ['test']
      }

      const createdNovel = await novelService.createNovel(novelData)
      const retrievedNovel = await novelService.getNovel(createdNovel.id)

      expect(retrievedNovel).toBeTruthy()
      expect(retrievedNovel!.id).toBe(createdNovel.id)
      expect(retrievedNovel!.title).toBe('Test Novel')
    })

    it('should return null for non-existent novel', async () => {
      const novel = await novelService.getNovel('non-existent-id')
      expect(novel).toBeNull()
    })

    it('should get all novels ordered by updated_at', async () => {
      const novel1 = await novelService.createNovel({
        title: 'First Novel',
        author: 'Author 1',
        description: 'Description 1',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        genre: [],
        tags: []
      })

      // Wait a bit to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10))

      const novel2 = await novelService.createNovel({
        title: 'Second Novel',
        author: 'Author 2',
        description: 'Description 2',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        genre: [],
        tags: []
      })

      const allNovels = await novelService.getAllNovels()

      expect(allNovels).toHaveLength(2)
      expect(allNovels[0].id).toBe(novel2.id) // Most recent first
      expect(allNovels[1].id).toBe(novel1.id)
    })

    it('should update a novel', async () => {
      const novel = await novelService.createNovel({
        title: 'Original Title',
        author: 'Original Author',
        description: 'Original Description',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        genre: [],
        tags: []
      })

      await novelService.updateNovel(novel.id, {
        title: 'Updated Title',
        wordCount: 1000
      })

      const updatedNovel = await novelService.getNovel(novel.id)

      expect(updatedNovel!.title).toBe('Updated Title')
      expect(updatedNovel!.wordCount).toBe(1000)
      expect(updatedNovel!.author).toBe('Original Author') // Unchanged
    })

    it('should delete a novel', async () => {
      const novel = await novelService.createNovel({
        title: 'To Be Deleted',
        author: 'Author',
        description: 'Description',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        genre: [],
        tags: []
      })

      await novelService.deleteNovel(novel.id)

      const deletedNovel = await novelService.getNovel(novel.id)
      expect(deletedNovel).toBeNull()
    })
  })

  describe('Chapter Operations', () => {
    let novel: Novel

    beforeEach(async () => {
      novel = await novelService.createNovel({
        title: 'Test Novel',
        author: 'Test Author',
        description: 'Test Description',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        genre: [],
        tags: []
      })
    })

    it('should create a chapter successfully', async () => {
      const chapterData = {
        novelId: novel.id,
        title: 'Chapter 1',
        content: 'Once upon a time...',
        wordCount: 100,
        orderIndex: 1,
        status: 'draft' as const
      }

      const chapter = await novelService.createChapter(chapterData)

      expect(chapter.id).toBeDefined()
      expect(chapter.title).toBe('Chapter 1')
      expect(chapter.novelId).toBe(novel.id)
      expect(chapter.orderIndex).toBe(1)
    })

    it('should get chapters by novel id ordered by index', async () => {
      const chapter1 = await novelService.createChapter({
        novelId: novel.id,
        title: 'Chapter 1',
        content: 'Content 1',
        wordCount: 100,
        orderIndex: 1,
        status: 'draft'
      })

      const chapter2 = await novelService.createChapter({
        novelId: novel.id,
        title: 'Chapter 2',
        content: 'Content 2',
        wordCount: 200,
        orderIndex: 2,
        status: 'draft'
      })

      const chapters = await novelService.getChaptersByNovelId(novel.id)

      expect(chapters).toHaveLength(2)
      expect(chapters[0].orderIndex).toBe(1)
      expect(chapters[1].orderIndex).toBe(2)
    })

    it('should update novel stats when chapter is created', async () => {
      await novelService.createChapter({
        novelId: novel.id,
        title: 'Chapter 1',
        content: 'Content with some words',
        wordCount: 150,
        orderIndex: 1,
        status: 'draft'
      })

      const updatedNovel = await novelService.getNovel(novel.id)

      expect(updatedNovel!.chapterCount).toBe(1)
      expect(updatedNovel!.wordCount).toBe(150)
    })

    it('should update chapter', async () => {
      const chapter = await novelService.createChapter({
        novelId: novel.id,
        title: 'Original Title',
        content: 'Original content',
        wordCount: 100,
        orderIndex: 1,
        status: 'draft'
      })

      await novelService.updateChapter(chapter.id, {
        title: 'Updated Title',
        content: 'Updated content with more words',
        wordCount: 200
      })

      const updatedChapter = (await novelService.getChaptersByNovelId(novel.id))[0]

      expect(updatedChapter.title).toBe('Updated Title')
      expect(updatedChapter.wordCount).toBe(200)
    })

    it('should delete chapter and update novel stats', async () => {
      const chapter = await novelService.createChapter({
        novelId: novel.id,
        title: 'Chapter 1',
        content: 'Content',
        wordCount: 100,
        orderIndex: 1,
        status: 'draft'
      })

      await novelService.deleteChapter(chapter.id)

      const chapters = await novelService.getChaptersByNovelId(novel.id)
      expect(chapters).toHaveLength(0)

      const updatedNovel = await novelService.getNovel(novel.id)
      expect(updatedNovel!.chapterCount).toBe(0)
      expect(updatedNovel!.wordCount).toBe(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Simulate database error by making the mock throw an error
      const originalInsert = mockDb.insert
      mockDb.insert = () => Promise.reject(new Error('Database connection failed'))

      await expect(novelService.createNovel({
        title: 'Test Novel',
        author: 'Test Author',
        description: 'Test Description',
        coverImage: '',
        wordCount: 0,
        chapterCount: 0,
        status: 'draft',
        genre: [],
        tags: []
      })).rejects.toThrow('Database connection failed')

      // Restore original method
      mockDb.insert = originalInsert
    })
  })
})