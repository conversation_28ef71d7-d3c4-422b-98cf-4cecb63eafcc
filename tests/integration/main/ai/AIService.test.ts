import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIService, AIProvider } from '@main/ai/AIService'
import { AIRequest, AIResponse } from '@shared/types'

// Mock AI Provider
class MockAIProvider implements AIProvider {
  private available: boolean
  private name: string
  private shouldFail: boolean = false

  constructor(name: string, available: boolean = true) {
    this.name = name
    this.available = available
  }

  async generate(request: AIRequest): Promise<AIResponse> {
    if (this.shouldFail) {
      throw new Error(`Provider ${this.name} failed`)
    }
    
    return {
      content: `Generated by ${this.name}: ${request.prompt}`,
      usage: {
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30
      },
      model: 'mock-model',
      provider: this.name
    }
  }

  isAvailable(): boolean {
    return this.available
  }

  getName(): string {
    return this.name
  }

  setShouldFail(shouldFail: boolean): void {
    this.shouldFail = shouldFail
  }
}

describe('AIService Integration Tests', () => {
  let aiService: AIService
  let mockProvider1: MockAIProvider
  let mockProvider2: MockAIProvider
  let mockProvider3: MockAIProvider

  beforeEach(() => {
    // Create mock providers
    mockProvider1 = new MockAIProvider('provider1', true)
    mockProvider2 = new MockAIProvider('provider2', true)
    mockProvider3 = new MockAIProvider('provider3', false) // Unavailable

    // Create AIService and replace providers
    aiService = new AIService()
    
    // Replace providers with mocks
    const providers = new Map()
    providers.set('provider1', mockProvider1)
    providers.set('provider2', mockProvider2)
    providers.set('provider3', mockProvider3)
    
    // @ts-ignore
    aiService.providers = providers
    // @ts-ignore
    aiService.defaultProvider = 'provider1'
  })

  describe('Basic Generation', () => {
    it('should generate content using default provider', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      const response = await aiService.generate(request)

      expect(response.content).toContain('Generated by provider1')
      expect(response.provider).toBe('provider1')
      expect(response.usage.totalTokens).toBe(30)
    })

    it('should generate content using specified provider', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      const response = await aiService.generate(request, 'provider2')

      expect(response.content).toContain('Generated by provider2')
      expect(response.provider).toBe('provider2')
    })

    it('should throw error for non-existent provider', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      await expect(aiService.generate(request, 'non-existent'))
        .rejects.toThrow('AI provider \'non-existent\' not found')
    })

    it('should throw error for unavailable provider', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      await expect(aiService.generate(request, 'provider3'))
        .rejects.toThrow('AI provider \'provider3\' is not available')
    })
  })

  describe('Fallback Mechanism', () => {
    it('should fallback to available providers when specified provider fails', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      // Make provider1 fail
      mockProvider1.setShouldFail(true)

      const response = await aiService.generate(request, 'provider1')

      expect(response.content).toContain('Generated by provider2')
      expect(response.provider).toBe('provider2')
    })

    it('should throw error when all providers fail', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      // Make all providers fail
      mockProvider1.setShouldFail(true)
      mockProvider2.setShouldFail(true)

      await expect(aiService.generate(request, 'provider1'))
        .rejects.toThrow('Provider provider1 failed')
    })
  })

  describe('Retry Mechanism', () => {
    it('should retry failed requests', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      let callCount = 0
      mockProvider1.setShouldFail(true)
      
      // Override generate method to count calls and succeed on second attempt
      const originalGenerate = mockProvider1.generate
      mockProvider1.generate = async (request: AIRequest) => {
        callCount++
        if (callCount === 1) {
          throw new Error('First attempt failed')
        }
        return originalGenerate.call(mockProvider1, request)
      }

      const response = await aiService.generateWithRetry(request, 'provider1', 3)

      expect(callCount).toBe(2)
      expect(response.content).toContain('Generated by provider1')
    })

    it('should throw error after max retries', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      mockProvider1.setShouldFail(true)

      await expect(aiService.generateWithRetry(request, 'provider1', 3))
        .rejects.toThrow('Provider provider1 failed')
    })
  })

  describe('Multiple Generation', () => {
    it('should generate content from multiple providers', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      const responses = await aiService.generateMultiple(request, ['provider1', 'provider2'])

      expect(responses).toHaveLength(2)
      expect(responses[0].content).toContain('Generated by provider1')
      expect(responses[1].content).toContain('Generated by provider2')
    })

    it('should return only successful responses', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      // Make provider2 fail
      mockProvider2.setShouldFail(true)

      const responses = await aiService.generateMultiple(request, ['provider1', 'provider2'])

      expect(responses).toHaveLength(1)
      expect(responses[0].content).toContain('Generated by provider1')
    })

    it('should throw error when no providers are available', async () => {
      const request: AIRequest = {
        prompt: 'Test prompt',
        context: 'Test context',
        maxTokens: 100,
        temperature: 0.7
      }

      // Make all providers unavailable
      mockProvider1.setShouldFail(true)
      mockProvider2.setShouldFail(true)

      await expect(aiService.generateMultiple(request, ['provider1', 'provider2']))
        .rejects.toThrow('No AI providers available')
    })
  })

  describe('Specialized Methods', () => {
    it('should continue writing', async () => {
      const context = '这是一个故事的开头...'
      const result = await aiService.continueWriting(context, 100, 'formal')

      expect(result).toContain('Generated by provider1')
      expect(typeof result).toBe('string')
    })

    it('should polish text', async () => {
      const text = '这是一段需要润色的文本'
      const result = await aiService.polishText(text, 'formal', ['grammar', 'style'])

      expect(result).toContain('Generated by provider1')
      expect(typeof result).toBe('string')
    })

    it('should provide plot recommendations', async () => {
      const currentPlot = '主角发现了一个秘密'
      const genre = 'mystery'
      const result = await aiService.plotRecommendation(currentPlot, genre, ['主角', '配角'])

      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeGreaterThan(0)
    })

    it('should analyze character relationships', async () => {
      const characters = [
        { name: '主角', description: '勇敢的英雄' },
        { name: '反派', description: '邪恶的对手' }
      ]
      const currentStory = '主角与反派展开了激烈的战斗'

      const result = await aiService.analyzeCharacterRelationships(characters, currentStory)

      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeGreaterThan(0)
    })

    it('should correct text errors', async () => {
      const text = '这有一段有错误的文本'
      const result = await aiService.correctText(text)

      expect(result).toContain('Generated by provider1')
      expect(typeof result).toBe('string')
    })
  })

  describe('Provider Management', () => {
    it('should return available providers', () => {
      const availableProviders = aiService.getAvailableProviders()

      expect(availableProviders).toContain('provider1')
      expect(availableProviders).toContain('provider2')
      expect(availableProviders).not.toContain('provider3')
    })

    it('should set default provider', () => {
      aiService.setDefaultProvider('provider2')
      expect(aiService.getDefaultProvider()).toBe('provider2')
    })

    it('should not set default provider for non-existent provider', () => {
      const originalDefault = aiService.getDefaultProvider()
      aiService.setDefaultProvider('non-existent')
      expect(aiService.getDefaultProvider()).toBe(originalDefault)
    })
  })
})