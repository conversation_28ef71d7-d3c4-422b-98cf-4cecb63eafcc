# 小说创作管理器 (Novel Creation Manager)

![License: MIT](https://img.shields.io/badge/license-MIT-blue.svg)
![Version: 1.0.0](https://img.shields.io/badge/version-1.0.0-brightgreen.svg)
![Platform: Windows, macOS, Linux](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)
![Framework: Electron](https://img.shields.io/badge/framework-Electron-47848F.svg)

一款专为小说创作者设计的智能写作助手，集创意构思、文章撰写、数据安全备份以及多格式导出于一体。采用现代化技术栈，支持跨平台运行，为创作者提供完整的写作解决方案。

## ✨ 核心功能

### 📝 智能写作编辑器
- **富文本编辑器**: 基于 Monaco Editor 的强大编辑体验
- **实时自动保存**: 防止数据丢失，支持版本历史
- **字数统计**: 实时显示字数、章节统计和写作进度
- **专注模式**: 无干扰的纯净写作环境

### 🤖 AI 写作助手
- **智能续写**: 基于上下文的智能文本生成
- **文本润色**: 语法检查、风格优化、内容提升
- **剧情推荐**: AI 驱动的情节发展建议
- **角色关系分析**: 可视化角色网络和互动模式
- **多模型支持**: OpenAI、智谱AI、通义千问、豆包等

### 📊 故事结构管理
- **大纲构建**: 模块化故事结构规划工具
- **角色管理**: 详细的角色信息库和关系图谱
- **素材库**: 分类存储研究资料、灵感和笔记
- **时间线管理**: 故事 chronology 可视化

### 💾 数据安全与备份
- **本地存储**: 基于 SQLite 的本地数据库
- **自动备份**: 定期备份和版本控制
- **数据恢复**: 完整的备份和恢复功能
- **隐私保护**: 本地存储，数据完全可控

### 📤 多格式导出
- **格式支持**: Markdown、TXT、HTML (DOC格式)
- **批量导出**: 支持整部小说或选择性导出
- **元数据包含**: 自动包含作者信息和作品元数据
- **自定义选项**: 灵活的导出配置

## 🛠 技术栈

### 前端技术
- **React 18**: 现代 React 框架，支持并发特性
- **TypeScript**: 类型安全的 JavaScript
- **Vite**: 极速的前端构建工具
- **Styled Components**: CSS-in-JS 样式解决方案
- **Zustand**: 轻量级状态管理
- **React Router**: 客户端路由

### 桌面应用
- **Electron 28**: 跨平台桌面应用框架
- **sql.js**: 纯 JavaScript SQLite 实现
- **Electron Builder**: 应用打包和分发

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks 管理
- **TypeScript**: 类型检查

## 🚀 快速开始

### 环境要求
- **Node.js**: 18.0 或更高版本
- **npm**: 8.0 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, Linux

### 安装和运行

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/novel-creation-manager.git
   cd novel-creation-manager
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发环境**
   ```bash
   npm run dev
   ```

4. **构建生产版本**
   ```bash
   npm run build
   ```

### 可用命令

```bash
# 开发模式
npm run dev                  # 启动开发服务器

# 构建相关
npm run build               # 构建生产版本
npm run build:vite          # 仅构建前端
npm run build:electron      # 仅打包 Electron 应用

# 代码质量
npm run lint                # 代码检查
npm run lint:fix            # 自动修复代码问题
npm run type-check          # TypeScript 类型检查

# 测试和清理
npm test                    # 运行测试
npm run clean               # 清理构建文件
```

## 📁 项目结构

```
novel-creation-manager/
├── src/
│   ├── main/                    # Electron 主进程
│   │   ├── index.ts            # 主进程入口
│   │   ├── database/           # 数据库服务
│   │   │   └── DatabaseService.ts
│   │   ├── ai/                 # AI 服务集成
│   │   │   ├── AIService.ts
│   │   │   └── providers/       # AI 提供商实现
│   │   ├── services/           # 业务服务
│   │   │   ├── NovelService.ts
│   │   │   ├── SettingsService.ts
│   │   │   └── StorageService.ts
│   │   └── storage/            # 存储管理
│   │       └── StorageService.ts
│   ├── renderer/               # 渲染进程 (前端)
│   │   ├── components/         # React 组件
│   │   │   ├── common/         # 通用组件
│   │   │   ├── editor/         # 编辑器组件
│   │   │   ├── ai/            # AI 功能组件
│   │   │   └── materials/     # 素材库组件
│   │   ├── pages/              # 页面组件
│   │   │   ├── Dashboard.tsx
│   │   │   ├── EditorPage.tsx
│   │   │   ├── MaterialsPage.tsx
│   │   │   └── OutlinePage.tsx
│   │   ├── stores/             # 状态管理
│   │   │   ├── novelStore.ts
│   │   │   ├── editorStore.ts
│   │   │   └── settingsStore.ts
│   │   ├── services/           # 前端服务
│   │   ├── utils/              # 工具函数
│   │   ├── types/              # TypeScript 类型定义
│   │   └── App.tsx             # 应用入口
│   ├── shared/                 # 共享代码
│   │   ├── types/              # 共享类型定义
│   │   └── utils/              # 共享工具函数
│   └── preload/                # 预加载脚本
├── dist/                       # 构建输出
├── release/                    # 发布包
├── public/                     # 静态资源
├── docs/                       # 文档
├── package.json               # 项目配置
├── vite.config.ts             # Vite 配置
├── tsconfig.json              # TypeScript 配置
├── tailwind.config.js         # Tailwind CSS 配置
└── README.md                  # 项目说明
```

## 🔧 配置说明

### AI 服务配置
应用支持多种 AI 服务提供商，需要在设置中配置相应的 API 密钥：

1. **OpenAI**: 需要 OpenAI API 密钥
2. **智谱AI**: 需要智谱AI API 密钥
3. **通义千问**: 需要阿里云 API 密钥
4. **豆包**: 需要字节跳动 API 密钥

### 数据库配置
- 使用 sql.js 实现，无需额外安装数据库
- 数据存储在用户数据目录
- 支持自动备份和手动备份

## 📖 详细文档

- [用户手册](docs/user-manual.md) - 详细的使用说明
- [技术架构](docs/architecture.md) - 系统架构和设计模式
- [API 文档](docs/api.md) - API 接口文档
- [部署指南](docs/deployment.md) - 构建和部署说明
- [贡献指南](docs/contributing.md) - 如何参与项目开发

## 🤝 贡献指南

我们欢迎任何形式的贡献！请阅读 [贡献指南](docs/contributing.md) 了解如何参与项目开发。

### 开发流程
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 使用 TypeScript 编写代码
- 遵循 ESLint 和 Prettier 规范
- 编写必要的测试和文档
- 确保代码通过类型检查

## 🐛 问题反馈

如果您遇到问题或有改进建议，请：
1. 查看 [常见问题](docs/faq.md)
2. 搜索现有的 [Issues](https://github.com/your-username/novel-creation-manager/issues)
3. 创建新的 Issue 描述问题

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目和贡献者：
- [Electron](https://www.electronjs.org/) - 跨平台桌面应用框架
- [React](https://reactjs.org/) - 用户界面构建库
- [Monaco Editor](https://microsoft.github.io/monaco-editor/) - 代码编辑器
- [sql.js](https://sql.js.org/) - JavaScript SQLite 库

## 📞 联系方式

- **项目主页**: https://github.com/your-username/novel-creation-manager
- **问题反馈**: https://github.com/your-username/novel-creation-manager/issues
- **邮件联系**: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请考虑给我们一个 Star！